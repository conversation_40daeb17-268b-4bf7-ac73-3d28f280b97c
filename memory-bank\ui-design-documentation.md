# UI设计文档 - OTA订单处理系统

## 设计概述

本系统采用现代化的响应式设计，注重用户体验和操作效率。设计风格简洁专业，符合企业级应用的视觉标准。

## 设计原则

### 1. 用户体验原则
- **简洁性**: 界面简洁明了，避免不必要的视觉干扰
- **一致性**: 保持整个系统的视觉和交互一致性
- **可用性**: 优化操作流程，减少用户学习成本
- **可访问性**: 支持键盘导航和屏幕阅读器
- **响应性**: 适配不同设备和屏幕尺寸

### 2. 视觉设计原则
- **层次感**: 通过颜色、大小、间距建立清晰的视觉层次
- **对比度**: 确保文字和背景有足够的对比度
- **留白**: 合理使用留白，提升阅读体验
- **品牌一致性**: 保持与企业品牌形象的一致性

## 设计系统

### 1. 色彩系统

#### 1.1 主色调
```css
:root {
  /* 主色调 - 蓝色系 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* 主色 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
}
```

#### 1.2 辅助色调
```css
:root {
  /* 成功色 - 绿色系 */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  /* 警告色 - 黄色系 */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* 错误色 - 红色系 */
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  /* 信息色 - 蓝色系 */
  --info-50: #f0f9ff;
  --info-500: #06b6d4;
  --info-600: #0891b2;
}
```

#### 1.3 中性色调
```css
:root {
  /* 灰色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 语义化颜色 */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --border-color: var(--gray-200);
  --background-primary: #ffffff;
  --background-secondary: var(--gray-50);
}
```

### 2. 字体系统

#### 2.1 字体族
```css
:root {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
               'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 
               'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 
               'Liberation Mono', 'Courier New', monospace;
}
```

#### 2.2 字体大小
```css
:root {
  /* 字体大小 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* 字重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### 3. 间距系统

```css
:root {
  /* 间距系统 - 基于4px网格 */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### 4. 圆角系统

```css
:root {
  /* 圆角系统 */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;
}
```

### 5. 阴影系统

```css
:root {
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}
```

## 组件设计

### 1. 按钮组件

#### 1.1 主要按钮
```css
.btn-primary {
  background-color: var(--primary-500);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary:disabled {
  background-color: var(--gray-300);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
```

#### 1.2 次要按钮
```css
.btn-secondary {
  background-color: white;
  color: var(--primary-500);
  border: 1px solid var(--primary-500);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--primary-50);
  border-color: var(--primary-600);
  color: var(--primary-600);
}
```

#### 1.3 危险按钮
```css
.btn-danger {
  background-color: var(--error-500);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background-color: var(--error-600);
}
```

### 2. 表单组件

#### 2.1 输入框
```css
.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-family: var(--font-sans);
  transition: all 0.2s ease;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-input:invalid {
  border-color: var(--error-500);
}

.form-input:invalid:focus {
  box-shadow: 0 0 0 3px var(--error-100);
}

.form-input:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}
```

#### 2.2 标签
```css
.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-500);
}
```

#### 2.3 错误信息
```css
.form-error {
  display: block;
  font-size: var(--text-xs);
  color: var(--error-500);
  margin-top: var(--space-1);
}
```

#### 2.4 帮助文本
```css
.form-help {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-top: var(--space-1);
}
```

### 3. 卡片组件

```css
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}
```

### 4. 模态框组件

```css
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(-20px);
  transition: all 0.3s ease;
}

.modal-overlay.active .modal {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}
```

### 5. 标签页组件

```css
.tabs {
  border-bottom: 1px solid var(--border-color);
}

.tab-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tab-item {
  margin-right: var(--space-1);
}

.tab-button {
  background: none;
  border: none;
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: var(--gray-50);
}

.tab-button.active {
  color: var(--primary-500);
  border-bottom-color: var(--primary-500);
}

.tab-content {
  padding: var(--space-6) 0;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}
```

### 6. 状态指示器

#### 6.1 加载动画
```css
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
```

#### 6.2 状态徽章
```css
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background-color: var(--success-100);
  color: var(--success-800);
}

.badge-warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.badge-error {
  background-color: var(--error-100);
  color: var(--error-800);
}

.badge-info {
  background-color: var(--info-100);
  color: var(--info-800);
}
```

### 7. 通知组件

```css
.notification {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  max-width: 400px;
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary-500);
  padding: var(--space-4);
  z-index: 1001;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  border-left-color: var(--success-500);
}

.notification-warning {
  border-left-color: var(--warning-500);
}

.notification-error {
  border-left-color: var(--error-500);
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.notification-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-base);
}

.notification-close:hover {
  background-color: var(--gray-100);
}

.notification-body {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}
```

## 页面布局

### 1. 整体布局结构

```html
<div class="app">
  <!-- 登录模态框 -->
  <div class="modal-overlay" id="loginModal">
    <div class="modal">
      <!-- 登录表单 -->
    </div>
  </div>
  
  <!-- 主应用界面 -->
  <div class="main-app" id="mainApp">
    <!-- 头部 -->
    <header class="app-header">
      <!-- 导航和用户信息 -->
    </header>
    
    <!-- 主内容区 -->
    <main class="app-main">
      <!-- 标签页导航 -->
      <div class="tabs">
        <!-- 标签页列表 -->
      </div>
      
      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 文字输入面板 -->
        <div class="tab-panel" id="textPanel">
          <!-- 文字输入组件 -->
        </div>
        
        <!-- 图片上传面板 -->
        <div class="tab-panel" id="imagePanel">
          <!-- 图片上传组件 -->
        </div>
      </div>
      
      <!-- OTA选择区域 -->
      <div class="ota-selection">
        <!-- OTA类型选择 -->
      </div>
      
      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <!-- 处理按钮 -->
      </div>
      
      <!-- 结果预览区域 -->
      <div class="result-preview">
        <!-- 结果展示和编辑 -->
      </div>
    </main>
    
    <!-- 状态栏 -->
    <footer class="app-footer">
      <!-- 状态信息 -->
    </footer>
  </div>
  
  <!-- 通知容器 -->
  <div class="notification-container" id="notificationContainer">
    <!-- 动态生成的通知 -->
  </div>
</div>
```

### 2. 响应式布局

```css
/* 移动设备 */
@media (max-width: 768px) {
  .app-main {
    padding: var(--space-4);
  }
  
  .modal {
    width: 95%;
    margin: var(--space-4);
  }
  
  .tab-list {
    flex-wrap: wrap;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .notification {
    top: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .app-main {
    padding: var(--space-6);
  }
  
  .content-area {
    max-width: 800px;
    margin: 0 auto;
  }
}

/* 桌面设备 */
@media (min-width: 1025px) {
  .app-main {
    padding: var(--space-8);
  }
  
  .content-area {
    max-width: 1000px;
    margin: 0 auto;
  }
}
```

## 交互设计

### 1. 动画效果

#### 1.1 页面过渡
```css
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

#### 1.2 微交互
```css
.button-press {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.hover-lift {
  transition: all 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
```

### 2. 状态反馈

#### 2.1 加载状态
```css
.loading-state {
  position: relative;
  pointer-events: none;
}

.loading-state::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.skeleton {
  background: linear-gradient(90deg, 
    var(--gray-200) 25%, 
    var(--gray-100) 50%, 
    var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
```

#### 2.2 错误状态
```css
.error-state {
  border-color: var(--error-500);
  background-color: var(--error-50);
}

.error-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
```

### 3. 焦点管理

```css
/* 键盘导航焦点样式 */
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-500);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

## 可访问性设计

### 1. 语义化HTML

```html
<!-- 使用语义化标签 -->
<main role="main" aria-label="订单处理主界面">
  <section aria-labelledby="input-section-title">
    <h2 id="input-section-title">订单信息输入</h2>
    <!-- 输入表单 -->
  </section>
  
  <section aria-labelledby="result-section-title">
    <h2 id="result-section-title">处理结果</h2>
    <!-- 结果展示 -->
  </section>
</main>

<!-- 表单标签关联 -->
<label for="customer-name">客户姓名</label>
<input id="customer-name" type="text" required aria-describedby="name-help">
<div id="name-help" class="form-help">请输入客户的真实姓名</div>

<!-- 按钮状态说明 -->
<button aria-label="处理订单" aria-describedby="process-status">
  处理订单
</button>
<div id="process-status" aria-live="polite">准备就绪</div>
```

### 2. ARIA属性

```html
<!-- 模态框 -->
<div class="modal-overlay" role="dialog" aria-modal="true" aria-labelledby="modal-title">
  <div class="modal">
    <h2 id="modal-title">登录系统</h2>
    <!-- 模态框内容 -->
  </div>
</div>

<!-- 标签页 -->
<div class="tabs" role="tablist" aria-label="订单输入方式">
  <button role="tab" aria-selected="true" aria-controls="text-panel" id="text-tab">
    文字输入
  </button>
  <button role="tab" aria-selected="false" aria-controls="image-panel" id="image-tab">
    图片上传
  </button>
</div>

<div id="text-panel" role="tabpanel" aria-labelledby="text-tab">
  <!-- 文字输入内容 -->
</div>

<!-- 状态更新 -->
<div aria-live="polite" aria-atomic="true" id="status-updates">
  <!-- 动态状态信息 -->
</div>

<!-- 错误信息 -->
<div role="alert" aria-live="assertive" id="error-messages">
  <!-- 错误信息 -->
</div>
```

### 3. 键盘导航

```javascript
// 键盘导航支持
class KeyboardNavigation {
    static init() {
        // Tab键导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.handleTabNavigation(e);
            }
            
            // Escape键关闭模态框
            if (e.key === 'Escape') {
                this.handleEscapeKey(e);
            }
            
            // Enter键激活按钮
            if (e.key === 'Enter' && e.target.matches('button, [role="button"]')) {
                e.target.click();
            }
            
            // 方向键导航标签页
            if ((e.key === 'ArrowLeft' || e.key === 'ArrowRight') && 
                e.target.matches('[role="tab"]')) {
                this.handleArrowNavigation(e);
            }
        });
    }
    
    static handleTabNavigation(e) {
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
    
    static handleEscapeKey(e) {
        const openModal = document.querySelector('.modal-overlay.active');
        if (openModal) {
            this.closeModal(openModal);
        }
    }
    
    static handleArrowNavigation(e) {
        const tabs = Array.from(document.querySelectorAll('[role="tab"]'));
        const currentIndex = tabs.indexOf(e.target);
        let nextIndex;
        
        if (e.key === 'ArrowLeft') {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
        } else {
            nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
        }
        
        tabs[nextIndex].focus();
        tabs[nextIndex].click();
    }
}
```

## 性能优化

### 1. CSS优化

```css
/* 使用CSS变量减少重复 */
:root {
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* 优化动画性能 */
.optimized-animation {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 减少重绘和重排 */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* 延迟加载非关键CSS */
@media (min-width: 1024px) {
  .desktop-only {
    /* 桌面特定样式 */
  }
}
```

### 2. 图片优化

```css
/* 响应式图片 */
.responsive-image {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

/* 图片懒加载占位符 */
.image-placeholder {
  background-color: var(--gray-200);
  background-image: linear-gradient(45deg, 
    var(--gray-100) 25%, transparent 25%, 
    transparent 75%, var(--gray-100) 75%);
  background-size: 20px 20px;
  animation: placeholder-shimmer 1.5s infinite;
}

@keyframes placeholder-shimmer {
  0% { background-position: 0 0; }
  100% { background-position: 40px 40px; }
}
```

## 主题系统

### 1. 深色主题

```css
[data-theme="dark"] {
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --background-primary: #111827;
  --background-secondary: #1f2937;
  --border-color: #374151;
  
  /* 调整组件颜色 */
  --card-background: #1f2937;
  --input-background: #374151;
  --modal-background: #1f2937;
}

/* 主题切换动画 */
* {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal);
}
```

### 2. 主题切换器

```javascript
class ThemeManager {
    static init() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
    
    static setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // 更新主题切换按钮状态
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.setAttribute('aria-label', 
                theme === 'dark' ? '切换到浅色主题' : '切换到深色主题'
            );
        }
    }
    
    static toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }
}
```

这份UI设计文档详细描述了OTA订单处理系统的完整视觉设计系统，包括设计原则、组件规范、布局结构、交互效果、可访问性和性能优化，为开发人员提供了全面的UI实现指南。