# API文档 - OTA订单处理系统

## API概述

本系统集成了两个主要的外部API服务：
1. **Google Gemini AI API** - 用于智能处理订单文本和图片
2. **GoMyHire API** - 用于系统认证和订单创建

## Google Gemini AI API

### 基础信息
- **服务提供商**: Google
- **API版本**: v1beta
- **认证方式**: API Key
- **请求格式**: JSON
- **响应格式**: JSON

### 端点信息

#### 1. 文本生成 (Gemini Pro)
```
POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={API_KEY}
```

**用途**: 处理文本格式的订单信息

**请求头**:
```http
Content-Type: application/json
```

**请求体结构**:
```json
{
  "contents": [
    {
      "parts": [
        {
          "text": "处理提示词 + 订单文本内容"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.7,
    "topK": 40,
    "topP": 0.95,
    "maxOutputTokens": 2048
  }
}
```

**响应结构**:
```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "AI生成的结构化订单数据(JSON格式)"
          }
        ]
      },
      "finishReason": "STOP",
      "index": 0,
      "safetyRatings": [...]
    }
  ],
  "promptFeedback": {
    "safetyRatings": [...]
  }
}
```

#### 2. 视觉理解 (Gemini Pro Vision)
```
POST https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent?key={API_KEY}
```

**用途**: 处理图片格式的订单信息

**请求头**:
```http
Content-Type: application/json
```

**请求体结构**:
```json
{
  "contents": [
    {
      "parts": [
        {
          "text": "请分析这张图片中的订单信息："
        },
        {
          "inline_data": {
            "mime_type": "image/jpeg",
            "data": "base64编码的图片数据"
          }
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.7,
    "topK": 40,
    "topP": 0.95,
    "maxOutputTokens": 2048
  }
}
```

**响应结构**: 与文本生成API相同

### 错误处理

**常见错误码**:
- `400 Bad Request`: 请求格式错误
- `401 Unauthorized`: API密钥无效
- `403 Forbidden`: 配额不足或权限不够
- `429 Too Many Requests`: 请求频率过高
- `500 Internal Server Error`: 服务器内部错误

**错误响应格式**:
```json
{
  "error": {
    "code": 400,
    "message": "错误描述",
    "status": "INVALID_ARGUMENT"
  }
}
```

### 使用限制
- **请求频率**: 每分钟60次请求
- **文本长度**: 单次请求最大30,720个字符
- **图片大小**: 最大20MB
- **图片格式**: JPEG, PNG, WebP, HEIC, HEIF

### 最佳实践
1. **错误重试**: 实现指数退避重试机制
2. **请求缓存**: 对相同输入进行结果缓存
3. **并发控制**: 限制同时进行的请求数量
4. **内容过滤**: 确保输入内容符合安全政策

## GoMyHire API

### 基础信息
- **服务提供商**: GoMyHire
- **API版本**: v1
- **基础URL**: `https://staging.gomyhire.com.my/api`
- **认证方式**: JWT Token (部分接口需要)
- **请求格式**: JSON
- **响应格式**: JSON

### 认证流程

#### 1. 用户登录
```
POST /login
```

**用途**: 用户认证，获取访问令牌

**请求头**:
```http
Content-Type: application/json
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "user_password"
}
```

**成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "邮箱或密码错误",
  "errors": {
    "email": ["邮箱格式不正确"],
    "password": ["密码不能为空"]
  }
}
```

### 数据获取接口

#### 1. 获取后台用户列表
```
GET /backend_users
```

**用途**: 获取可分配订单的后台用户列表

**认证**: 需要Bearer Token

**请求头**:
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应结构**:
```json
[
  {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "role": "operator",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 2,
    "name": "李四",
    "email": "<EMAIL>",
    "role": "supervisor",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 2. 获取服务子分类
```
GET /sub_category
```

**用途**: 获取服务类型的子分类列表

**认证**: 需要Bearer Token

**请求头**:
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应结构**:
```json
[
  {
    "id": 1,
    "name": "机场接送",
    "category_id": 1,
    "category_name": "交通服务",
    "description": "机场到酒店/目的地的接送服务",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 2,
    "name": "酒店接送",
    "category_id": 1,
    "category_name": "交通服务",
    "description": "酒店到机场/目的地的接送服务",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 3. 获取车型列表
```
GET /car_types
```

**用途**: 获取可用的车型列表

**认证**: 需要Bearer Token

**请求头**:
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应结构**:
```json
[
  {
    "id": 1,
    "name": "经济型轿车",
    "capacity": 4,
    "luggage_capacity": 2,
    "description": "适合1-4人乘坐的经济型车辆",
    "price_per_km": 2.50,
    "base_price": 15.00,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  {
    "id": 2,
    "name": "商务车",
    "capacity": 7,
    "luggage_capacity": 5,
    "description": "适合5-7人乘坐的商务车辆",
    "price_per_km": 4.00,
    "base_price": 25.00,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### 订单创建接口

#### 创建订单
```
POST /create_order
```

**用途**: 创建新的订单

**认证**: 不需要Bearer Token

**请求头**:
```http
Content-Type: application/json
```

**请求体结构**:
```json
{
  // 必填字段
  "sub_category_id": 1,
  "ota_reference_number": "OTA-2024-001",
  "car_type_id": 1,
  "incharge_by_backend_user_id": 1,
  
  // 可选字段
  "customer_name": "张三",
  "customer_contact": "+60123456789",
  "flight_info": "MH123 - 14:30 arrival",
  "pickup": "吉隆坡国际机场 (KLIA)",
  "destination": "吉隆坡希尔顿酒店",
  "date": "2024-12-20",
  "time": "15:00",
  "passenger_number": 2,
  "notes": "客户要求英文司机"
}
```

**字段说明**:

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sub_category_id | integer | 是 | 服务子分类ID |
| ota_reference_number | string | 是 | OTA参考号码 |
| car_type_id | integer | 是 | 车型ID |
| incharge_by_backend_user_id | integer | 是 | 负责人ID |
| customer_name | string | 否 | 客户姓名 |
| customer_contact | string | 否 | 客户联系方式 |
| flight_info | string | 否 | 航班信息 |
| pickup | string | 否 | 上车地点 |
| destination | string | 否 | 目的地 |
| date | string | 否 | 服务日期 (YYYY-MM-DD) |
| time | string | 否 | 服务时间 (HH:MM) |
| passenger_number | integer | 否 | 乘客数量 |
| notes | string | 否 | 备注信息 |

**成功响应**:
```json
{
  "success": true,
  "message": "订单创建成功",
  "order": {
    "id": 12345,
    "order_number": "GMH-2024-12345",
    "ota_reference_number": "OTA-2024-001",
    "status": "pending",
    "customer_name": "张三",
    "customer_contact": "+60123456789",
    "pickup": "吉隆坡国际机场 (KLIA)",
    "destination": "吉隆坡希尔顿酒店",
    "date": "2024-12-20",
    "time": "15:00",
    "passenger_number": 2,
    "car_type": {
      "id": 1,
      "name": "经济型轿车"
    },
    "sub_category": {
      "id": 1,
      "name": "机场接送"
    },
    "incharge_by": {
      "id": 1,
      "name": "张三"
    },
    "created_at": "2024-12-19T10:30:00Z",
    "updated_at": "2024-12-19T10:30:00Z"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "订单创建失败",
  "errors": {
    "sub_category_id": ["服务子分类ID不能为空"],
    "ota_reference_number": ["OTA参考号码不能为空"],
    "car_type_id": ["车型ID不能为空"],
    "incharge_by_backend_user_id": ["负责人ID不能为空"]
  }
}
```

### 错误处理

**HTTP状态码**:
- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权访问
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `429 Too Many Requests`: 请求频率过高
- `500 Internal Server Error`: 服务器内部错误

**标准错误响应格式**:
```json
{
  "success": false,
  "message": "错误描述",
  "errors": {
    "field_name": ["具体错误信息"]
  },
  "error_code": "ERROR_CODE",
  "timestamp": "2024-12-19T10:30:00Z"
}
```

### 使用限制
- **请求频率**: 每分钟100次请求
- **Token有效期**: 24小时
- **并发连接**: 最多10个并发连接
- **请求大小**: 最大1MB

## API集成实现

### JavaScript实现示例

#### Gemini AI服务类
```javascript
class GeminiService {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models';
        this.config = {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048
        };
    }
    
    async processText(text, prompt) {
        const url = `${this.baseURL}/gemini-pro:generateContent?key=${this.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: `${prompt}\n\n订单内容：\n${text}`
                }]
            }],
            generationConfig: this.config
        };
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error(`Gemini API错误: ${response.status}`);
            }
            
            const data = await response.json();
            return this.parseResponse(data);
        } catch (error) {
            console.error('Gemini API调用失败:', error);
            throw error;
        }
    }
    
    async processImage(imageData, prompt) {
        const url = `${this.baseURL}/gemini-pro-vision:generateContent?key=${this.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [
                    {
                        text: `${prompt}\n\n请分析这张图片中的订单信息：`
                    },
                    {
                        inline_data: {
                            mime_type: "image/jpeg",
                            data: imageData
                        }
                    }
                ]
            }],
            generationConfig: this.config
        };
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error(`Gemini Vision API错误: ${response.status}`);
            }
            
            const data = await response.json();
            return this.parseResponse(data);
        } catch (error) {
            console.error('Gemini Vision API调用失败:', error);
            throw error;
        }
    }
    
    parseResponse(response) {
        try {
            if (!response.candidates || response.candidates.length === 0) {
                throw new Error('AI未返回有效结果');
            }
            
            const text = response.candidates[0].content.parts[0].text;
            
            // 尝试解析JSON
            const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[1]);
            }
            
            return JSON.parse(text);
        } catch (error) {
            return {
                success: false,
                error: error.message,
                raw_text: response.candidates?.[0]?.content?.parts?.[0]?.text
            };
        }
    }
}
```

#### GoMyHire API服务类
```javascript
class GoMyHireAPI {
    constructor() {
        this.baseURL = 'https://staging.gomyhire.com.my/api';
        this.token = null;
    }
    
    setToken(token) {
        this.token = token;
    }
    
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        
        if (this.token && !endpoint.includes('/login')) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    async login(email, password) {
        const response = await this.request('/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });
        
        if (response.token) {
            this.setToken(response.token);
        }
        
        return response;
    }
    
    async getBackendUsers() {
        return await this.request('/backend_users');
    }
    
    async getSubCategories() {
        return await this.request('/sub_category');
    }
    
    async getCarTypes() {
        return await this.request('/car_types');
    }
    
    async createOrder(orderData) {
        return await this.request('/create_order', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    }
}
```

### 错误处理最佳实践

#### 统一错误处理
```javascript
class APIErrorHandler {
    static handle(error, context = '') {
        console.error(`[${context}] API错误:`, error);
        
        // 网络错误
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                type: 'network',
                message: '网络连接失败，请检查网络设置',
                retry: true
            };
        }
        
        // API错误
        if (error.message.includes('401')) {
            return {
                type: 'auth',
                message: '认证失败，请重新登录',
                retry: false
            };
        }
        
        if (error.message.includes('429')) {
            return {
                type: 'rate_limit',
                message: '请求过于频繁，请稍后重试',
                retry: true,
                retryAfter: 60000
            };
        }
        
        if (error.message.includes('500')) {
            return {
                type: 'server',
                message: '服务器错误，请稍后重试',
                retry: true
            };
        }
        
        // 默认错误
        return {
            type: 'unknown',
            message: error.message || '未知错误',
            retry: false
        };
    }
}
```

#### 重试机制
```javascript
class RetryHandler {
    static async withRetry(fn, maxRetries = 3, delay = 1000) {
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                const errorInfo = APIErrorHandler.handle(error);
                
                if (!errorInfo.retry || i === maxRetries) {
                    throw error;
                }
                
                const retryDelay = errorInfo.retryAfter || delay * Math.pow(2, i);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }
}
```

### 性能优化

#### 请求缓存
```javascript
class APICache {
    constructor(ttl = 300000) { // 5分钟默认TTL
        this.cache = new Map();
        this.ttl = ttl;
    }
    
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }
    
    set(key, data) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + this.ttl
        });
    }
    
    clear() {
        this.cache.clear();
    }
}
```

#### 并发控制
```javascript
class ConcurrencyController {
    constructor(maxConcurrent = 3) {
        this.maxConcurrent = maxConcurrent;
        this.running = 0;
        this.queue = [];
    }
    
    async execute(fn) {
        return new Promise((resolve, reject) => {
            this.queue.push({ fn, resolve, reject });
            this.process();
        });
    }
    
    async process() {
        if (this.running >= this.maxConcurrent || this.queue.length === 0) {
            return;
        }
        
        this.running++;
        const { fn, resolve, reject } = this.queue.shift();
        
        try {
            const result = await fn();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            this.process();
        }
    }
}
```

## 安全考虑

### API密钥管理
1. **不要在代码中硬编码API密钥**
2. **使用环境变量或配置文件**
3. **定期轮换API密钥**
4. **限制API密钥的权限范围**

### 数据传输安全
1. **始终使用HTTPS**
2. **验证SSL证书**
3. **不在URL中传递敏感信息**
4. **对敏感数据进行加密**

### 输入验证
1. **验证所有用户输入**
2. **防止注入攻击**
3. **限制输入长度**
4. **过滤恶意内容**

### 错误信息安全
1. **不暴露系统内部信息**
2. **统一错误响应格式**
3. **记录详细错误日志**
4. **向用户显示友好错误信息**

## 监控和调试

### API调用监控
```javascript
class APIMonitor {
    constructor() {
        this.metrics = {
            requests: 0,
            successes: 0,
            failures: 0,
            totalTime: 0,
            errors: []
        };
    }
    
    recordRequest(endpoint, duration, success, error = null) {
        this.metrics.requests++;
        this.metrics.totalTime += duration;
        
        if (success) {
            this.metrics.successes++;
        } else {
            this.metrics.failures++;
            this.metrics.errors.push({
                endpoint,
                error: error?.message,
                timestamp: new Date().toISOString()
            });
        }
    }
    
    getStats() {
        return {
            ...this.metrics,
            successRate: this.metrics.requests > 0 ? 
                (this.metrics.successes / this.metrics.requests * 100).toFixed(2) + '%' : '0%',
            averageTime: this.metrics.requests > 0 ? 
                Math.round(this.metrics.totalTime / this.metrics.requests) + 'ms' : '0ms'
        };
    }
    
    reset() {
        this.metrics = {
            requests: 0,
            successes: 0,
            failures: 0,
            totalTime: 0,
            errors: []
        };
    }
}
```

### 调试工具
```javascript
class APIDebugger {
    static logRequest(method, url, data = null) {
        if (CONFIG.DEBUG) {
            console.group(`🚀 API请求: ${method} ${url}`);
            if (data) {
                console.log('请求数据:', data);
            }
            console.log('时间:', new Date().toISOString());
            console.groupEnd();
        }
    }
    
    static logResponse(url, response, duration) {
        if (CONFIG.DEBUG) {
            console.group(`✅ API响应: ${url}`);
            console.log('响应数据:', response);
            console.log('耗时:', duration + 'ms');
            console.groupEnd();
        }
    }
    
    static logError(url, error, duration) {
        console.group(`❌ API错误: ${url}`);
        console.error('错误信息:', error);
        console.log('耗时:', duration + 'ms');
        console.groupEnd();
    }
}
```

这份API文档详细描述了OTA订单处理系统中使用的所有API接口，包括请求格式、响应结构、错误处理、安全考虑和最佳实践，为开发人员提供了完整的API集成指南。