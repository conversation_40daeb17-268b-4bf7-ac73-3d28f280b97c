# 车型选择逻辑测试指南

## 🎯 新的车型选择规则

根据您的要求，我已经优化了车型选择逻辑：

### 优先级顺序
1. **明确指定车型** - 如果订单中明确写明车型，优先选择指定车型
2. **根据人数选择** - 如果没有指定车型，根据人数进行智能选择
3. **默认车型** - 如果没有人数信息，默认选择 **Comfort 5 Seater (ID: 6)**

## 🧪 测试场景

### 场景1：明确指定车型
**输入订单内容**：
```
1.28接机：KE671 22.20抵达
客人姓名：张三
车型：MPV
人数：4人
```

**预期结果**：
- 选择包含"MPV"的车型（如：Standard Size MPV）
- 选择原因：订单指定车型: MPV

### 场景2：有人数，无指定车型
**输入订单内容**：
```
1.29接机：SQ123 18.30抵达
客人姓名：李四
人数：2人
行李：1件
```

**预期结果**：
- 选择：Compact 5 Seater (ID: 5)
- 选择原因：2人1件行李，选择紧凑型5座车

### 场景3：有人数（中等团队）
**输入订单内容**：
```
1.30送机：AK378 16.20起飞
客人姓名：王五
人数：6人
行李：4件
```

**预期结果**：
- 选择：Standard Size MPV (ID: 16) 或其他6座车型
- 选择原因：6人4件行李，选择6座MPV

### 场景4：有人数（大型团队）
**输入订单内容**：
```
1.31包车：KL到云顶
客人姓名：赵六
人数：10人
行李：8件
```

**预期结果**：
- 选择：12 Seater MPV (ID: 30) 或 10 Seater MPV/Van (ID: 20)
- 选择原因：中型团队10人，选择适合的大型车辆

### 场景5：无人数信息（默认情况）
**输入订单内容**：
```
1.32接机：CX123 20.15抵达
客人姓名：孙七
备注：普通接机服务
```

**预期结果**：
- 选择：**Comfort 5 Seater (ID: 6)**
- 选择原因：未提供人数信息，使用默认车型：Comfort 5 Seater

### 场景6：人数为0或无效
**输入订单内容**：
```
1.33送机：TR123 14.30起飞
客人姓名：周八
人数：0人
```

**预期结果**：
- 选择：**Comfort 5 Seater (ID: 6)**
- 选择原因：未提供人数信息，使用默认车型：Comfort 5 Seater

## 📊 人数与车型对应表

| 人数范围 | 推荐车型 | 车型ID | 座位数 |
|---------|---------|--------|--------|
| 明确指定车型 | 按指定车型匹配 | - | - |
| 1-2人 | Compact 5 Seater | 5 | 4座 |
| 3-4人 | Comfort 5 Seater | 6 | 4座 |
| 5-6人 | Standard Size MPV | 16 | 6座 |
| 7-8人 | Mid Size SUV | 15 | 7座 |
| 9-11人 | 10 Seater MPV/Van | 20 | 9座 |
| 12-15人 | 12 Seater MPV | 30 | 11座 |
| 16-29人 | 14 Seater Van | 23 | 12座 |
| 30+人 | 30 Seat Mini Bus | 25 | 30座 |
| **无人数** | **Comfort 5 Seater** | **6** | **4座** |

## 🔍 验证方法

### 1. 查看控制台日志
在浏览器开发者工具中查找以下日志：

```javascript
[智能选择] 开始选择车型
- orderId: "订单ID"
- passengerNumber: "人数信息"
- specifiedCarType: "指定车型"
- availableCarTypes: 13

[智能选择] 车型选择参数分析
- originalPassengerStr: "原始人数字符串"
- extractedPassengerNumber: 提取的数字
- luggageNumber: 行李数
- hasSpecifiedCarType: true/false

[智能选择] 车型选择完成
- selectedCarTypeId: 选择的车型ID
- selectedCarTypeName: "车型名称"
- selectedSeatNumber: 座位数
- selectionReason: "选择原因"
```

### 2. 验证选择逻辑
- **有指定车型**：检查是否优先匹配指定车型
- **有人数信息**：检查是否根据人数选择合适车型
- **无人数信息**：检查是否默认选择 Comfort 5 Seater (ID: 6)

### 3. 边界情况测试
- 人数为0：应选择默认车型
- 人数为空字符串：应选择默认车型
- 人数为非数字：应选择默认车型
- 超大团队（30+人）：应选择最大座位数车型

## ✅ 验证清单

### 基本功能验证
- [ ] 明确指定车型时，优先选择指定车型
- [ ] 有人数时，根据人数智能选择车型
- [ ] 无人数时，默认选择 Comfort 5 Seater (ID: 6)
- [ ] 选择原因日志清晰明确

### 人数范围验证
- [ ] 1-2人选择 Compact 5 Seater
- [ ] 3-4人选择 Comfort 5 Seater
- [ ] 5-6人选择 6座MPV
- [ ] 7+人选择相应大型车辆
- [ ] 无人数选择 Comfort 5 Seater

### 边界情况验证
- [ ] 人数为0的处理
- [ ] 人数为空的处理
- [ ] 人数为非数字的处理
- [ ] 超大团队的处理
- [ ] 找不到匹配车型时的回退机制

### 日志记录验证
- [ ] 车型选择过程有详细日志
- [ ] 参数分析日志完整
- [ ] 选择原因清晰记录
- [ ] 最终结果正确显示

## 🎯 成功标准

车型选择功能成功的标准：

1. **优先级正确**：指定车型 > 人数判断 > 默认车型
2. **默认车型准确**：无人数时始终选择 Comfort 5 Seater (ID: 6)
3. **人数匹配合理**：根据人数选择合适大小的车型
4. **日志记录完整**：每个决策步骤都有清晰的日志
5. **边界处理完善**：异常情况有合理的回退机制

## 🚀 测试步骤

1. **登录系统**并打开浏览器控制台
2. **输入测试订单**（按照上述场景）
3. **处理订单**并观察车型选择过程
4. **检查日志**确认选择逻辑正确
5. **验证结果**确保选择的车型符合预期

完成测试后，您将拥有一个能够智能选择车型的系统，确保在任何情况下都能做出合理的车型选择决策！
