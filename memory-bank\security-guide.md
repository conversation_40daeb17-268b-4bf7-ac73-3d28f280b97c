# OTA订单处理系统安全配置指南

## 概述

本文档提供了OTA订单处理系统的安全配置指南，包括API密钥管理、数据保护、访问控制和安全部署等方面的最佳实践。

## 🔐 API密钥安全管理

### 1. 内部使用场景说明
**注意：本系统为内部使用，API密钥暴露风险可控。**

对于内部使用的系统，可以采用简化的密钥管理策略：
- 直接在配置文件中管理API密钥
- 通过访问控制限制系统使用范围
- 定期审查和更新密钥

### 1.1 当前配置（内部使用）
```javascript
// config.js - 内部使用配置
const config = {
    // Gemini API密钥 - 内部使用
    geminiApiKey: 'your-gemini-api-key-here',
    
    // GoMyHire API配置
    apiBaseUrl: 'https://api.gomyhire.com',
    
    // 内部访问控制
    allowedDomains: ['localhost', '127.0.0.1', 'internal.company.com'],
    
    // 开发环境标识
    isDevelopment: true
};
```

### 1.2 内部使用安全建议

#### 访问控制
```javascript
// 简单的访问控制检查
function validateInternalAccess() {
    const hostname = window.location.hostname;
    const allowedHosts = ['localhost', '127.0.0.1', 'internal.company.com'];
    
    if (!allowedHosts.includes(hostname)) {
        throw new Error('未授权的访问域名');
    }
}

// 在应用初始化时检查
validateInternalAccess();
```

#### 密钥管理最佳实践
```javascript
// 密钥配置管理
class InternalKeyManager {
    constructor() {
        this.validateEnvironment();
    }

    validateEnvironment() {
        // 确保在内部环境中运行
        if (window.location.protocol !== 'https:' && 
            !['localhost', '127.0.0.1'].includes(window.location.hostname)) {
            console.warn('建议在HTTPS环境下使用');
        }
    }

    getApiKey() {
        // 内部使用直接返回配置的密钥
        return config.geminiApiKey;
    }

    // 可选：密钥有效性检查
    async validateApiKey() {
        try {
            const response = await fetch('https://generativelanguage.googleapis.com/v1/models', {
                headers: {
                    'Authorization': `Bearer ${this.getApiKey()}`
                }
            });
            return response.ok;
        } catch (error) {
            console.error('API密钥验证失败:', error);
            return false;
        }
    }
}
```

### 1.3 生产环境升级建议
如果将来需要部署到生产环境，建议采用以下安全措施：

#### 环境变量配置
```javascript
// 生产环境配置示例
const config = {
    geminiApiKey: process.env.GEMINI_API_KEY || '',
    apiBaseUrl: process.env.API_BASE_URL || 'https://api.gomyhire.com'
};
```

#### 后端代理方案
```javascript
// 通过后端代理调用API
const response = await fetch('/api/gemini/process', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
    },
    body: JSON.stringify(orderData)
});
```

### 2. 密钥轮换策略

```javascript
class KeyRotationManager {
    constructor() {
        this.currentKey = null;
        this.backupKey = null;
        this.rotationInterval = 30 * 24 * 60 * 60 * 1000; // 30天
    }
    
    async rotateKeys() {
        try {
            // 获取新密钥
            const newKey = await this.generateNewKey();
            
            // 测试新密钥
            const isValid = await this.validateKey(newKey);
            if (!isValid) {
                throw new Error('新密钥验证失败');
            }
            
            // 更新密钥
            this.backupKey = this.currentKey;
            this.currentKey = newKey;
            
            // 更新配置
            await this.updateSecureConfig({
                currentKey: this.currentKey,
                backupKey: this.backupKey,
                rotatedAt: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('密钥轮换失败:', error);
            // 使用备用密钥
            if (this.backupKey) {
                this.currentKey = this.backupKey;
            }
        }
    }
}
```

## 🛡️ 输入验证和数据清理

### 1. 全面的输入验证

```javascript
class SecurityValidator {
    // XSS防护
    static sanitizeHTML(input) {
        const div = document.createElement('div');
        div.textContent = input;
        return div.innerHTML;
    }
    
    // SQL注入防护（虽然是前端，但防止传递恶意数据）
    static sanitizeSQL(input) {
        return input.replace(/[';"\\]/g, '');
    }
    
    // 文件名安全检查
    static validateFileName(fileName) {
        const dangerousPatterns = [
            /\.\.\//,  // 路径遍历
            /[<>:"|?*]/,  // 非法字符
            /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i  // Windows保留名
        ];
        
        return !dangerousPatterns.some(pattern => pattern.test(fileName));
    }
    
    // 订单数据验证
    static validateOrderData(data) {
        const sanitized = {};
        const errors = [];
        
        // 客户姓名验证
        if (data.customerName) {
            sanitized.customerName = this.sanitizeHTML(data.customerName.trim());
            if (!/^[\u4e00-\u9fa5a-zA-Z\s]{2,50}$/.test(sanitized.customerName)) {
                errors.push('客户姓名格式不正确');
            }
        }
        
        // 电话号码验证
        if (data.phone) {
            sanitized.phone = data.phone.replace(/[^\d+\-\s]/g, '');
            if (!/^(\+?6?01[0-46-9]\d{7,8}|\+?6?0[2-9]\d{7,8})$/.test(sanitized.phone)) {
                errors.push('电话号码格式不正确');
            }
        }
        
        // 地址验证
        if (data.address) {
            sanitized.address = this.sanitizeHTML(data.address.trim());
            if (sanitized.address.length > 200) {
                errors.push('地址长度不能超过200个字符');
            }
        }
        
        return {
            isValid: errors.length === 0,
            sanitizedData: sanitized,
            errors
        };
    }
}
```

### 2. 文件上传安全

```javascript
class FileUploadSecurity {
    static validateImageFile(file) {
        const errors = [];
        
        // 文件类型检查
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            errors.push('不支持的文件类型');
        }
        
        // 文件大小检查
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            errors.push('文件大小超过限制');
        }
        
        // 文件名检查
        if (!SecurityValidator.validateFileName(file.name)) {
            errors.push('文件名包含非法字符');
        }
        
        // 文件头检查（魔数验证）
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const arr = new Uint8Array(e.target.result).subarray(0, 4);
                const header = Array.from(arr).map(b => b.toString(16).padStart(2, '0')).join('');
                
                const validHeaders = {
                    'ffd8ffe0': 'image/jpeg',
                    'ffd8ffe1': 'image/jpeg',
                    'ffd8ffe2': 'image/jpeg',
                    '89504e47': 'image/png',
                    '47494638': 'image/gif',
                    '52494646': 'image/webp'
                };
                
                if (!validHeaders[header.substring(0, 8)]) {
                    errors.push('文件头验证失败，可能不是有效的图片文件');
                }
                
                resolve({
                    isValid: errors.length === 0,
                    errors
                });
            };
            reader.readAsArrayBuffer(file.slice(0, 4));
        });
    }
    
    static async scanFileForMalware(file) {
        // 简单的恶意文件检测
        const suspiciousPatterns = [
            /<script[^>]*>/i,
            /javascript:/i,
            /vbscript:/i,
            /onload=/i,
            /onerror=/i
        ];
        
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                const isSuspicious = suspiciousPatterns.some(pattern => 
                    pattern.test(content)
                );
                
                resolve({
                    isSafe: !isSuspicious,
                    reason: isSuspicious ? '文件包含可疑内容' : null
                });
            };
            reader.readAsText(file);
        });
    }
}
```

## 🔒 访问控制和认证

### 1. 会话管理

```javascript
class SessionManager {
    constructor() {
        this.sessionTimeout = 30 * 60 * 1000; // 30分钟
        this.warningTime = 5 * 60 * 1000; // 5分钟警告
        this.lastActivity = Date.now();
        this.warningShown = false;
        
        this.initActivityTracking();
        this.startSessionMonitoring();
    }
    
    initActivityTracking() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });
    }
    
    updateActivity() {
        this.lastActivity = Date.now();
        this.warningShown = false;
    }
    
    startSessionMonitoring() {
        setInterval(() => {
            const timeSinceActivity = Date.now() - this.lastActivity;
            
            if (timeSinceActivity >= this.sessionTimeout) {
                this.handleSessionExpiry();
            } else if (timeSinceActivity >= this.sessionTimeout - this.warningTime && !this.warningShown) {
                this.showSessionWarning();
                this.warningShown = true;
            }
        }, 60000); // 每分钟检查一次
    }
    
    showSessionWarning() {
        const remainingTime = Math.ceil((this.sessionTimeout - (Date.now() - this.lastActivity)) / 60000);
        
        if (confirm(`您的会话将在${remainingTime}分钟后过期，是否继续？`)) {
            this.updateActivity();
        }
    }
    
    handleSessionExpiry() {
        alert('会话已过期，请重新登录');
        this.logout();
    }
    
    logout() {
        // 清除敏感数据
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        sessionStorage.clear();
        
        // 重定向到登录页
        window.location.reload();
    }
}
```

### 2. 权限控制

```javascript
class PermissionManager {
    constructor() {
        this.userPermissions = new Set();
        this.loadUserPermissions();
    }
    
    async loadUserPermissions() {
        try {
            const response = await fetch('/api/user/permissions', {
                headers: {
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            
            const data = await response.json();
            this.userPermissions = new Set(data.permissions);
        } catch (error) {
            console.error('加载用户权限失败:', error);
        }
    }
    
    hasPermission(permission) {
        return this.userPermissions.has(permission);
    }
    
    requirePermission(permission) {
        if (!this.hasPermission(permission)) {
            throw new Error(`缺少权限: ${permission}`);
        }
    }
    
    // 功能级权限检查
    canCreateOrder() {
        return this.hasPermission('order:create');
    }
    
    canViewOrders() {
        return this.hasPermission('order:read');
    }
    
    canEditOrder() {
        return this.hasPermission('order:update');
    }
    
    canDeleteOrder() {
        return this.hasPermission('order:delete');
    }
}
```

## 🌐 网络安全

### 1. HTTPS强制和安全头

```html
<!-- 在index.html中添加安全头 -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: blob:; 
               connect-src 'self' https://generativelanguage.googleapis.com https://staging.gomyhire.com.my; 
               frame-ancestors 'none';">

<meta http-equiv="X-Content-Type-Options" content="nosniff">
<meta http-equiv="X-Frame-Options" content="DENY">
<meta http-equiv="X-XSS-Protection" content="1; mode=block">
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
<meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">
```

### 2. CORS配置

```javascript
class CORSManager {
    static validateOrigin(origin) {
        const allowedOrigins = [
            'https://yourdomain.com',
            'https://staging.yourdomain.com',
            'http://localhost:3000' // 仅开发环境
        ];
        
        return allowedOrigins.includes(origin);
    }
    
    static setupCORS() {
        // 检查当前域名
        const currentOrigin = window.location.origin;
        
        if (!this.validateOrigin(currentOrigin)) {
            console.warn('当前域名未在允许列表中');
        }
    }
}
```

## 🔍 安全监控和日志

### 1. 安全事件监控

```javascript
class SecurityMonitor {
    constructor() {
        this.securityEvents = [];
        this.suspiciousActivities = new Map();
        this.initMonitoring();
    }
    
    initMonitoring() {
        // 监控异常API调用
        this.monitorAPIUsage();
        
        // 监控登录尝试
        this.monitorLoginAttempts();
        
        // 监控文件上传
        this.monitorFileUploads();
    }
    
    recordSecurityEvent(type, details) {
        const event = {
            type,
            details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            ip: this.getClientIP(),
            sessionId: this.getSessionId()
        };
        
        this.securityEvents.push(event);
        
        // 检查是否为可疑活动
        if (this.isSuspiciousActivity(event)) {
            this.handleSuspiciousActivity(event);
        }
        
        // 定期发送安全日志
        if (this.securityEvents.length >= 10) {
            this.sendSecurityLogs();
        }
    }
    
    isSuspiciousActivity(event) {
        const suspiciousPatterns = {
            'login_failure': { threshold: 5, timeWindow: 300000 }, // 5分钟内5次失败
            'api_error': { threshold: 10, timeWindow: 600000 }, // 10分钟内10次错误
            'file_upload_rejected': { threshold: 3, timeWindow: 300000 } // 5分钟内3次被拒绝
        };
        
        const pattern = suspiciousPatterns[event.type];
        if (!pattern) return false;
        
        const key = `${event.type}_${event.details.userId || 'anonymous'}`;
        const now = Date.now();
        
        if (!this.suspiciousActivities.has(key)) {
            this.suspiciousActivities.set(key, []);
        }
        
        const activities = this.suspiciousActivities.get(key);
        
        // 清理过期记录
        const validActivities = activities.filter(time => 
            now - time < pattern.timeWindow
        );
        
        validActivities.push(now);
        this.suspiciousActivities.set(key, validActivities);
        
        return validActivities.length >= pattern.threshold;
    }
    
    handleSuspiciousActivity(event) {
        console.warn('检测到可疑活动:', event);
        
        // 记录高优先级安全事件
        this.recordSecurityEvent('suspicious_activity', {
            originalEvent: event,
            severity: 'high'
        });
        
        // 可以添加额外的安全措施，如临时锁定账户
        if (event.type === 'login_failure') {
            this.temporaryLockAccount(event.details.userId);
        }
    }
    
    async sendSecurityLogs() {
        try {
            await fetch('/api/security/logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    events: this.securityEvents,
                    timestamp: new Date().toISOString()
                })
            });
            
            this.securityEvents = [];
        } catch (error) {
            console.error('发送安全日志失败:', error);
        }
    }
}
```

## 📋 安全检查清单

### 部署前安全检查

- [ ] API密钥已从代码中移除
- [ ] 所有用户输入都经过验证和清理
- [ ] 文件上传限制已配置
- [ ] HTTPS已启用
- [ ] 安全头已配置
- [ ] 会话超时已设置
- [ ] 错误信息不暴露敏感信息
- [ ] 日志记录已配置
- [ ] 权限控制已实施
- [ ] 依赖项安全扫描已完成

### 运行时安全监控

- [ ] 异常登录尝试监控
- [ ] API调用频率监控
- [ ] 文件上传安全扫描
- [ ] 错误率监控
- [ ] 性能异常监控
- [ ] 安全日志定期审查

## 🚨 应急响应计划

### 1. 安全事件响应流程

```javascript
class IncidentResponse {
    static async handleSecurityIncident(incident) {
        const severity = this.assessSeverity(incident);
        
        switch (severity) {
            case 'critical':
                await this.handleCriticalIncident(incident);
                break;
            case 'high':
                await this.handleHighSeverityIncident(incident);
                break;
            case 'medium':
                await this.handleMediumSeverityIncident(incident);
                break;
            default:
                await this.handleLowSeverityIncident(incident);
        }
    }
    
    static async handleCriticalIncident(incident) {
        // 1. 立即通知管理员
        await this.notifyAdministrators(incident, 'critical');
        
        // 2. 临时禁用受影响功能
        await this.disableAffectedFeatures(incident);
        
        // 3. 记录详细日志
        await this.logIncident(incident, 'critical');
        
        // 4. 启动应急程序
        await this.activateEmergencyProcedures();
    }
    
    static assessSeverity(incident) {
        const criticalPatterns = [
            'data_breach',
            'unauthorized_access',
            'system_compromise'
        ];
        
        const highPatterns = [
            'multiple_login_failures',
            'api_abuse',
            'malware_detected'
        ];
        
        if (criticalPatterns.includes(incident.type)) {
            return 'critical';
        } else if (highPatterns.includes(incident.type)) {
            return 'high';
        } else {
            return 'medium';
        }
    }
}
```

## 📖 安全培训和意识

### 开发团队安全指南

1. **代码审查重点**
   - 检查硬编码的敏感信息
   - 验证输入验证逻辑
   - 确认错误处理不泄露信息
   - 检查权限控制实现

2. **安全编码实践**
   - 始终验证和清理用户输入
   - 使用参数化查询防止注入攻击
   - 实施最小权限原则
   - 定期更新依赖项

3. **安全测试**
   - 定期进行渗透测试
   - 使用自动化安全扫描工具
   - 进行代码安全审计
   - 测试错误处理和边界条件

## 总结

安全是一个持续的过程，需要在开发、部署和运维的各个阶段都保持警惕。本指南提供了基础的安全配置和最佳实践，但应根据实际需求和威胁模型进行调整和扩展。

定期审查和更新安全措施，保持对最新安全威胁的了解，是维护系统安全的关键。