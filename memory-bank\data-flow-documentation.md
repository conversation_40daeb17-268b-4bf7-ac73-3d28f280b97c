# 数据流程文档 - OTA订单处理系统

## 数据流程概述

本系统的数据流程涵盖了从用户输入到订单创建的完整链路，包括数据采集、处理、转换、验证和存储等环节。

## 整体数据流程图

```
用户输入 → 数据预处理 → AI处理 → 数据验证 → 结果展示 → 用户编辑 → 订单创建 → 状态反馈
    ↓           ↓           ↓         ↓         ↓         ↓         ↓         ↓
  原始数据   标准化数据   结构化数据  验证数据   UI数据   修正数据   API数据   响应数据
```

## 详细数据流程

### 1. 数据输入阶段

#### 1.1 文本输入流程
```
用户输入文本 → 文本预处理 → 格式验证 → 数据暂存
```

**输入数据结构**:
```javascript
{
  type: 'text',
  content: '原始文本内容',
  timestamp: '2024-12-19T10:30:00Z',
  source: 'manual_input'
}
```

**预处理步骤**:
1. 去除多余空格和换行
2. 统一字符编码
3. 长度验证（最大10000字符）
4. 特殊字符过滤

**预处理后数据结构**:
```javascript
{
  type: 'text',
  content: '清理后的文本内容',
  originalLength: 1500,
  processedLength: 1450,
  timestamp: '2024-12-19T10:30:00Z',
  source: 'manual_input',
  preprocessed: true
}
```

#### 1.2 图片输入流程
```
用户上传图片 → 图片验证 → 格式转换 → Base64编码 → 数据暂存
```

**输入数据结构**:
```javascript
{
  type: 'image',
  file: File对象,
  timestamp: '2024-12-19T10:30:00Z',
  source: 'file_upload'
}
```

**验证规则**:
- 文件大小：最大20MB
- 文件格式：JPEG, PNG, WebP, HEIC, HEIF
- 图片尺寸：最大4096x4096像素

**处理后数据结构**:
```javascript
{
  type: 'image',
  originalName: 'order_screenshot.jpg',
  mimeType: 'image/jpeg',
  size: 2048576, // 字节
  width: 1920,
  height: 1080,
  base64Data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
  timestamp: '2024-12-19T10:30:00Z',
  source: 'file_upload',
  processed: true
}
```

### 2. AI处理阶段

#### 2.1 OTA类型识别流程
```
输入数据 → OTA识别提示词 → Gemini API → 识别结果 → 类型确定
```

**识别提示词模板**:
```
请分析以下订单内容，识别OTA类型：

可选类型：
1. Chong Dealer - 重庆经销商订单
2. Auto Detect - 自动识别其他OTA
3. Other - 其他类型

请返回JSON格式：
{
  "ota_type": "识别的类型",
  "confidence": 0.95,
  "reasoning": "识别理由"
}

订单内容：
[用户输入的内容]
```

**识别结果数据结构**:
```javascript
{
  ota_type: 'Chong Dealer',
  confidence: 0.95,
  reasoning: '包含重庆地区特征和经销商标识',
  timestamp: '2024-12-19T10:30:00Z',
  processing_time: 1200 // 毫秒
}
```

#### 2.2 订单信息提取流程
```
确定OTA类型 → 获取处理提示词 → Gemini API处理 → 结构化数据 → 数据验证
```

**Chong Dealer处理提示词**:
```
你是一个专业的OTA订单处理助手，请按照以下规则处理订单信息：

## 日期处理规则
1. 识别订单中的所有日期信息
2. 将相对日期转换为绝对日期
3. 验证日期的合理性
4. 统一日期格式为YYYY-MM-DD

## 时间计算规则
1. 航班到达时间 + 1小时 = 接机时间
2. 酒店退房时间 - 3小时 = 送机时间
3. 考虑交通拥堵，适当调整时间

## 地点标准化规则
1. 机场名称使用标准英文名称
2. 酒店名称使用官方英文名称
3. 地址信息保持完整性

## 联系方式处理
1. 电话号码标准化为国际格式
2. 生成OTA参考号码：OTA-YYYYMMDD-XXX

## 输出格式
请返回以下JSON格式的结构化数据：

{
  "customer_name": "客户姓名",
  "customer_contact": "+60123456789",
  "flight_info": "航班信息",
  "pickup": "上车地点（英文标准名称）",
  "destination": "目的地（英文标准名称）",
  "date": "2024-12-20",
  "time": "15:00",
  "passenger_number": 2,
  "ota_reference_number": "OTA-20241220-001",
  "notes": "备注信息",
  "processing_details": {
    "date_corrections": ["修正说明"],
    "time_calculations": ["计算说明"],
    "location_standardizations": ["标准化说明"]
  }
}

订单内容：
[用户输入的内容]
```

**AI处理结果数据结构**:
```javascript
{
  // 主要订单信息
  customer_name: '张三',
  customer_contact: '+60123456789',
  flight_info: 'MH123 - 14:30 arrival from Beijing',
  pickup: 'Kuala Lumpur International Airport (KLIA)',
  destination: 'Hilton Kuala Lumpur',
  date: '2024-12-20',
  time: '15:30',
  passenger_number: 2,
  ota_reference_number: 'OTA-20241220-001',
  notes: '客户要求英文司机，行李较多',
  
  // 处理详情
  processing_details: {
    date_corrections: [
      '将"明天"转换为2024-12-20',
      '验证日期在合理范围内'
    ],
    time_calculations: [
      '航班14:30到达 + 1小时处理时间 = 15:30接机时间',
      '考虑KLIA到市区交通时间'
    ],
    location_standardizations: [
      '"吉隆坡机场"标准化为"Kuala Lumpur International Airport (KLIA)"',
      '"希尔顿酒店"标准化为"Hilton Kuala Lumpur"'
    ]
  },
  
  // 元数据
  ai_processing: {
    model: 'gemini-pro',
    processing_time: 2500,
    confidence: 0.92,
    timestamp: '2024-12-19T10:32:30Z'
  }
}
```

### 3. 数据验证阶段

#### 3.1 数据完整性验证
```
AI处理结果 → 必填字段检查 → 数据格式验证 → 业务规则验证 → 验证报告
```

**验证规则配置**:
```javascript
const validationRules = {
  customer_name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/
  },
  customer_contact: {
    required: true,
    type: 'string',
    pattern: /^\+\d{1,3}\d{8,15}$/
  },
  date: {
    required: true,
    type: 'string',
    pattern: /^\d{4}-\d{2}-\d{2}$/,
    validator: (value) => {
      const date = new Date(value);
      const now = new Date();
      const maxDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
      return date >= now && date <= maxDate;
    }
  },
  time: {
    required: true,
    type: 'string',
    pattern: /^\d{2}:\d{2}$/
  },
  passenger_number: {
    required: true,
    type: 'number',
    min: 1,
    max: 20
  }
};
```

**验证结果数据结构**:
```javascript
{
  isValid: true,
  errors: [],
  warnings: [
    {
      field: 'time',
      message: '建议确认接机时间是否合理',
      severity: 'warning'
    }
  ],
  validatedData: {
    // 验证通过的数据
  },
  validationDetails: {
    totalFields: 10,
    validFields: 10,
    errorFields: 0,
    warningFields: 1
  }
}
```

### 4. 用户交互阶段

#### 4.1 结果展示流程
```
验证数据 → UI数据转换 → 表单渲染 → 用户查看 → 编辑操作
```

**UI数据结构**:
```javascript
{
  formData: {
    customer_name: {
      value: '张三',
      label: '客户姓名',
      type: 'text',
      required: true,
      editable: true,
      validation: 'valid'
    },
    customer_contact: {
      value: '+60123456789',
      label: '联系电话',
      type: 'tel',
      required: true,
      editable: true,
      validation: 'valid'
    },
    // ... 其他字段
  },
  metadata: {
    aiConfidence: 0.92,
    processingTime: 2500,
    lastModified: '2024-12-19T10:32:30Z',
    hasWarnings: true,
    warningCount: 1
  }
}
```

#### 4.2 用户编辑流程
```
用户修改 → 实时验证 → 数据更新 → 状态同步 → 保存变更
```

**编辑事件数据结构**:
```javascript
{
  field: 'customer_contact',
  oldValue: '+60123456789',
  newValue: '+60987654321',
  timestamp: '2024-12-19T10:35:00Z',
  source: 'user_edit',
  validation: {
    isValid: true,
    errors: [],
    warnings: []
  }
}
```

### 5. 订单创建阶段

#### 5.1 API数据构建流程
```
用户确认 → 数据映射 → API格式转换 → 必填字段补充 → 最终验证
```

**数据映射规则**:
```javascript
const fieldMapping = {
  // UI字段 → API字段
  'customer_name': 'customer_name',
  'customer_contact': 'customer_contact',
  'flight_info': 'flight_info',
  'pickup': 'pickup',
  'destination': 'destination',
  'date': 'date',
  'time': 'time',
  'passenger_number': 'passenger_number',
  'ota_reference_number': 'ota_reference_number',
  'notes': 'notes'
};

const requiredApiFields = {
  'sub_category_id': 1, // 默认值：机场接送
  'car_type_id': 1, // 默认值：经济型轿车
  'incharge_by_backend_user_id': null // 需要从系统数据获取
};
```

**API请求数据结构**:
```javascript
{
  // 必填字段
  sub_category_id: 1,
  ota_reference_number: 'OTA-20241220-001',
  car_type_id: 1,
  incharge_by_backend_user_id: 1,
  
  // 可选字段
  customer_name: '张三',
  customer_contact: '+60123456789',
  flight_info: 'MH123 - 14:30 arrival from Beijing',
  pickup: 'Kuala Lumpur International Airport (KLIA)',
  destination: 'Hilton Kuala Lumpur',
  date: '2024-12-20',
  time: '15:30',
  passenger_number: 2,
  notes: '客户要求英文司机，行李较多'
}
```

#### 5.2 订单创建响应处理
```
API响应 → 状态解析 → 成功/失败处理 → 用户反馈 → 数据存储
```

**成功响应处理**:
```javascript
{
  success: true,
  order: {
    id: 12345,
    order_number: 'GMH-2024-12345',
    status: 'pending',
    created_at: '2024-12-19T10:40:00Z'
  },
  ui_feedback: {
    type: 'success',
    title: '订单创建成功',
    message: '订单号：GMH-2024-12345',
    actions: [
      { label: '查看订单', action: 'view_order' },
      { label: '创建新订单', action: 'new_order' }
    ]
  }
}
```

**失败响应处理**:
```javascript
{
  success: false,
  errors: {
    sub_category_id: ['服务子分类ID不能为空'],
    customer_contact: ['电话号码格式不正确']
  },
  ui_feedback: {
    type: 'error',
    title: '订单创建失败',
    message: '请检查以下字段：',
    field_errors: {
      sub_category_id: '服务子分类ID不能为空',
      customer_contact: '电话号码格式不正确'
    },
    actions: [
      { label: '重新提交', action: 'retry_submit' },
      { label: '编辑订单', action: 'edit_order' }
    ]
  }
}
```

## 数据存储策略

### 1. 本地存储结构

#### 1.1 用户会话数据
```javascript
// localStorage key: 'ota_user_session'
{
  token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  user: {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: 'admin'
  },
  loginTime: '2024-12-19T09:00:00Z',
  lastActivity: '2024-12-19T10:40:00Z',
  expiresAt: '2024-12-20T09:00:00Z'
}
```

#### 1.2 系统配置数据
```javascript
// localStorage key: 'ota_system_config'
{
  gemini_api_key: 'encrypted_api_key',
  default_settings: {
    sub_category_id: 1,
    car_type_id: 1,
    incharge_by_backend_user_id: 1
  },
  ui_preferences: {
    theme: 'light',
    language: 'zh-CN',
    auto_save: true
  },
  last_updated: '2024-12-19T10:00:00Z'
}
```

#### 1.3 订单草稿数据
```javascript
// localStorage key: 'ota_order_draft'
{
  id: 'draft_' + Date.now(),
  type: 'text', // 'text' | 'image'
  original_input: '原始输入内容',
  processed_data: {
    // AI处理后的结构化数据
  },
  user_edits: [
    {
      field: 'customer_contact',
      oldValue: '+60123456789',
      newValue: '+60987654321',
      timestamp: '2024-12-19T10:35:00Z'
    }
  ],
  status: 'editing', // 'processing' | 'editing' | 'ready' | 'submitted'
  created_at: '2024-12-19T10:30:00Z',
  updated_at: '2024-12-19T10:35:00Z'
}
```

#### 1.4 系统缓存数据
```javascript
// localStorage key: 'ota_system_cache'
{
  backend_users: {
    data: [...],
    cached_at: '2024-12-19T10:00:00Z',
    expires_at: '2024-12-19T10:05:00Z'
  },
  sub_categories: {
    data: [...],
    cached_at: '2024-12-19T10:00:00Z',
    expires_at: '2024-12-19T10:05:00Z'
  },
  car_types: {
    data: [...],
    cached_at: '2024-12-19T10:00:00Z',
    expires_at: '2024-12-19T10:05:00Z'
  }
}
```

### 2. 数据生命周期管理

#### 2.1 数据清理策略
```javascript
class DataLifecycleManager {
    static cleanupExpiredData() {
        const now = new Date();
        
        // 清理过期的缓存数据
        const cache = this.getCache();
        Object.keys(cache).forEach(key => {
            if (cache[key].expires_at && new Date(cache[key].expires_at) < now) {
                delete cache[key];
            }
        });
        this.saveCache(cache);
        
        // 清理过期的会话数据
        const session = this.getSession();
        if (session && session.expiresAt && new Date(session.expiresAt) < now) {
            this.clearSession();
        }
        
        // 清理旧的草稿数据（超过7天）
        const drafts = this.getDrafts();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const validDrafts = drafts.filter(draft => 
            new Date(draft.created_at) > sevenDaysAgo
        );
        this.saveDrafts(validDrafts);
    }
    
    static scheduleCleanup() {
        // 每小时执行一次清理
        setInterval(() => {
            this.cleanupExpiredData();
        }, 60 * 60 * 1000);
    }
}
```

#### 2.2 数据备份策略
```javascript
class DataBackupManager {
    static exportData() {
        const data = {
            session: this.getSession(),
            config: this.getConfig(),
            drafts: this.getDrafts(),
            cache: this.getCache(),
            exported_at: new Date().toISOString(),
            version: '1.0.0'
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ota_backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
    
    static importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // 验证数据格式
                    if (!data.version || !data.exported_at) {
                        throw new Error('无效的备份文件格式');
                    }
                    
                    // 恢复数据
                    if (data.session) this.saveSession(data.session);
                    if (data.config) this.saveConfig(data.config);
                    if (data.drafts) this.saveDrafts(data.drafts);
                    if (data.cache) this.saveCache(data.cache);
                    
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }
}
```

## 数据安全措施

### 1. 敏感数据加密
```javascript
class DataEncryption {
    static encrypt(data, key) {
        // 使用AES加密敏感数据
        const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), key).toString();
        return encrypted;
    }
    
    static decrypt(encryptedData, key) {
        try {
            const bytes = CryptoJS.AES.decrypt(encryptedData, key);
            const decrypted = bytes.toString(CryptoJS.enc.Utf8);
            return JSON.parse(decrypted);
        } catch (error) {
            throw new Error('数据解密失败');
        }
    }
    
    static generateKey() {
        return CryptoJS.lib.WordArray.random(256/8).toString();
    }
}
```

### 2. 数据验证和清理
```javascript
class DataSanitizer {
    static sanitizeInput(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除脚本标签
            .replace(/<[^>]*>/g, '') // 移除HTML标签
            .replace(/javascript:/gi, '') // 移除JavaScript协议
            .trim();
    }
    
    static validatePhoneNumber(phone) {
        const pattern = /^\+\d{1,3}\d{8,15}$/;
        return pattern.test(phone);
    }
    
    static validateEmail(email) {
        const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return pattern.test(email);
    }
    
    static validateDate(date) {
        const dateObj = new Date(date);
        const now = new Date();
        const maxDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        
        return dateObj instanceof Date && 
               !isNaN(dateObj) && 
               dateObj >= now && 
               dateObj <= maxDate;
    }
}
```

## 性能优化策略

### 1. 数据缓存优化
```javascript
class DataCacheOptimizer {
    static optimizeCache() {
        const cache = this.getCache();
        const now = new Date();
        
        // 清理过期数据
        Object.keys(cache).forEach(key => {
            if (cache[key].expires_at && new Date(cache[key].expires_at) < now) {
                delete cache[key];
            }
        });
        
        // 压缩数据
        Object.keys(cache).forEach(key => {
            if (cache[key].data && Array.isArray(cache[key].data)) {
                cache[key].data = this.compressArray(cache[key].data);
            }
        });
        
        this.saveCache(cache);
    }
    
    static compressArray(array) {
        // 移除重复项
        const unique = array.filter((item, index, self) => 
            index === self.findIndex(t => t.id === item.id)
        );
        
        // 只保留必要字段
        return unique.map(item => ({
            id: item.id,
            name: item.name,
            // 其他必要字段
        }));
    }
}
```

### 2. 数据传输优化
```javascript
class DataTransferOptimizer {
    static compressRequest(data) {
        // 移除空值和未定义值
        const cleaned = Object.keys(data).reduce((acc, key) => {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                acc[key] = data[key];
            }
            return acc;
        }, {});
        
        return cleaned;
    }
    
    static batchRequests(requests, batchSize = 5) {
        const batches = [];
        for (let i = 0; i < requests.length; i += batchSize) {
            batches.push(requests.slice(i, i + batchSize));
        }
        return batches;
    }
    
    static async processBatches(batches, processor) {
        const results = [];
        
        for (const batch of batches) {
            const batchResults = await Promise.all(
                batch.map(request => processor(request))
            );
            results.push(...batchResults);
            
            // 批次间延迟，避免API限流
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return results;
    }
}
```

这份数据流程文档详细描述了OTA订单处理系统中的完整数据流程，包括数据输入、处理、验证、存储和安全措施，为开发人员提供了全面的数据处理指南。