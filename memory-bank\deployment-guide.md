# 部署指南 - OTA订单处理系统

## 部署概述

OTA订单处理系统是一个纯前端应用，采用静态文件部署方式，无需后端服务器。本指南详细说明了系统的部署流程、配置要求和最佳实践。

## 系统要求

### 1. 运行环境要求

#### 客户端要求
- **浏览器支持**:
  - Chrome 90+ (推荐)
  - Firefox 88+
  - Safari 14+
  - Edge 90+
- **JavaScript**: ES2020+ 支持
- **网络**: HTTPS连接（生产环境必需）
- **存储**: 支持LocalStorage（最少5MB可用空间）

#### 服务器要求
- **Web服务器**: 任何支持静态文件的Web服务器
  - Apache HTTP Server 2.4+
  - Nginx 1.18+
  - IIS 10+
  - Node.js + Express
  - Python HTTP Server
  - 或任何CDN服务
- **HTTPS**: 生产环境必须支持SSL/TLS
- **CORS**: 支持跨域请求配置

### 2. 外部服务依赖

#### Google Gemini AI API
- **API密钥**: 需要有效的Gemini API密钥
- **配额**: 建议每月至少1000次API调用配额
- **网络**: 需要访问 `https://generativelanguage.googleapis.com`

#### GoMyHire API
- **API访问**: 需要GoMyHire API访问权限
- **认证**: 需要有效的用户账户
- **网络**: 需要访问GoMyHire API端点

## 部署方式

### 1. 本地开发部署

#### 使用Python HTTP Server
```bash
# 进入项目目录
cd /path/to/ota-order-processing

# 启动Python HTTP服务器（Python 3）
python -m http.server 8000

# 或使用Python 2
python -m SimpleHTTPServer 8000

# 访问地址
http://localhost:8000
```

#### 使用Node.js HTTP Server
```bash
# 安装http-server（全局）
npm install -g http-server

# 进入项目目录
cd /path/to/ota-order-processing

# 启动服务器
http-server -p 8000 -c-1

# 访问地址
http://localhost:8000
```

#### 使用Live Server（VS Code扩展）
1. 安装Live Server扩展
2. 右键点击`index.html`
3. 选择"Open with Live Server"
4. 自动在浏览器中打开

### 2. 生产环境部署

#### Apache HTTP Server部署

**1. 配置虚拟主机**
```apache
# /etc/apache2/sites-available/ota-order.conf
<VirtualHost *:80>
    ServerName ota-order.yourdomain.com
    DocumentRoot /var/www/ota-order-processing
    
    # 重定向到HTTPS
    Redirect permanent / https://ota-order.yourdomain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName ota-order.yourdomain.com
    DocumentRoot /var/www/ota-order-processing
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    SSLCertificateChainFile /path/to/chain.crt
    
    # 安全头设置
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://generativelanguage.googleapis.com https://api.gomyhire.com;"
    
    # 缓存配置
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
        Header append Cache-Control "public"
    </LocationMatch>
    
    # HTML文件不缓存
    <LocationMatch "\.html$">
        ExpiresActive On
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </LocationMatch>
    
    # 启用压缩
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>
    
    # 错误页面
    ErrorDocument 404 /index.html
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/ota-order_error.log
    CustomLog ${APACHE_LOG_DIR}/ota-order_access.log combined
</VirtualHost>
```

**2. 启用站点**
```bash
# 启用站点
sudo a2ensite ota-order.conf

# 启用必要的模块
sudo a2enmod ssl
sudo a2enmod headers
sudo a2enmod expires
sudo a2enmod deflate
sudo a2enmod rewrite

# 重启Apache
sudo systemctl restart apache2
```

#### Nginx部署

**1. 配置Nginx**
```nginx
# /etc/nginx/sites-available/ota-order
server {
    listen 80;
    server_name ota-order.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ota-order.yourdomain.com;
    
    root /var/www/ota-order-processing;
    index index.html;
    
    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://generativelanguage.googleapis.com https://api.gomyhire.com;";
    
    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 缓存配置
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 主要路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 日志配置
    access_log /var/log/nginx/ota-order_access.log;
    error_log /var/log/nginx/ota-order_error.log;
}
```

**2. 启用站点**
```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/ota-order /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

#### CDN部署（推荐）

**1. AWS CloudFront + S3**
```bash
# 创建S3存储桶
aws s3 mb s3://ota-order-processing-bucket

# 配置存储桶为静态网站
aws s3 website s3://ota-order-processing-bucket \
    --index-document index.html \
    --error-document index.html

# 上传文件
aws s3 sync . s3://ota-order-processing-bucket \
    --exclude "*.md" \
    --exclude ".git/*" \
    --exclude "memory-bank/*"

# 设置缓存策略
aws s3 cp s3://ota-order-processing-bucket/index.html \
    s3://ota-order-processing-bucket/index.html \
    --metadata-directive REPLACE \
    --cache-control "no-cache, no-store, must-revalidate"
```

**CloudFront配置**:
```json
{
  "DistributionConfig": {
    "CallerReference": "ota-order-processing-2024",
    "Comment": "OTA Order Processing System",
    "DefaultRootObject": "index.html",
    "Origins": {
      "Quantity": 1,
      "Items": [
        {
          "Id": "S3-ota-order-processing",
          "DomainName": "ota-order-processing-bucket.s3.amazonaws.com",
          "S3OriginConfig": {
            "OriginAccessIdentity": ""
          }
        }
      ]
    },
    "DefaultCacheBehavior": {
      "TargetOriginId": "S3-ota-order-processing",
      "ViewerProtocolPolicy": "redirect-to-https",
      "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
      "Compress": true
    },
    "CustomErrorResponses": {
      "Quantity": 1,
      "Items": [
        {
          "ErrorCode": 404,
          "ResponsePagePath": "/index.html",
          "ResponseCode": "200",
          "ErrorCachingMinTTL": 300
        }
      ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
  }
}
```

**2. Netlify部署**
```bash
# 安装Netlify CLI
npm install -g netlify-cli

# 登录Netlify
netlify login

# 初始化项目
netlify init

# 部署
netlify deploy --prod
```

**netlify.toml配置**:
```toml
[build]
  publish = "."
  command = "echo 'No build required'"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://generativelanguage.googleapis.com https://api.gomyhire.com;"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
```

**3. Vercel部署**
```bash
# 安装Vercel CLI
npm install -g vercel

# 登录Vercel
vercel login

# 部署
vercel --prod
```

**vercel.json配置**:
```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        },
        {
          "key": "Content-Security-Policy",
          "value": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://generativelanguage.googleapis.com https://api.gomyhire.com;"
        }
      ]
    },
    {
      "source": "/(.*\\.(css|js|png|jpg|jpeg|gif|ico|svg))$",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    },
    {
      "source": "/(.*\\.html)$",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-cache, no-store, must-revalidate"
        }
      ]
    }
  ]
}
```

## 配置管理

### 1. 环境配置

#### 开发环境配置
```javascript
// config.js - 开发环境
const CONFIG = {
    // 环境标识
    ENVIRONMENT: 'development',
    
    // API配置
    GOMYHIRE_API: {
        BASE_URL: 'https://dev-api.gomyhire.com',
        TIMEOUT: 10000
    },
    
    // Gemini AI配置
    GEMINI_AI: {
        API_KEY: 'your-dev-api-key-here',
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
        MODEL: 'gemini-1.5-flash',
        TIMEOUT: 30000
    },
    
    // 调试配置
    DEBUG: {
        ENABLED: true,
        LOG_LEVEL: 'debug',
        SHOW_API_RESPONSES: true
    },
    
    // 功能开关
    FEATURES: {
        AUTO_SAVE: true,
        OFFLINE_MODE: false,
        ANALYTICS: false
    }
};
```

#### 生产环境配置
```javascript
// config.js - 生产环境
const CONFIG = {
    // 环境标识
    ENVIRONMENT: 'production',
    
    // API配置
    GOMYHIRE_API: {
        BASE_URL: 'https://api.gomyhire.com',
        TIMEOUT: 15000
    },
    
    // Gemini AI配置
    GEMINI_AI: {
        API_KEY: '', // 通过环境变量或用户输入设置
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
        MODEL: 'gemini-1.5-flash',
        TIMEOUT: 45000
    },
    
    // 调试配置
    DEBUG: {
        ENABLED: false,
        LOG_LEVEL: 'error',
        SHOW_API_RESPONSES: false
    },
    
    // 功能开关
    FEATURES: {
        AUTO_SAVE: true,
        OFFLINE_MODE: true,
        ANALYTICS: true
    }
};
```

### 2. 安全配置

#### API密钥管理
```javascript
// 安全的API密钥管理
class SecureConfig {
    constructor() {
        this.apiKeys = new Map();
        this.initializeFromStorage();
    }
    
    // 从安全存储初始化
    initializeFromStorage() {
        try {
            const encrypted = localStorage.getItem('secure_config');
            if (encrypted) {
                const decrypted = this.decrypt(encrypted);
                const config = JSON.parse(decrypted);
                this.apiKeys.set('gemini', config.gemini_api_key);
            }
        } catch (error) {
            console.warn('Failed to load secure config:', error);
        }
    }
    
    // 设置API密钥
    setApiKey(service, key) {
        if (!key || typeof key !== 'string') {
            throw new Error('Invalid API key');
        }
        
        this.apiKeys.set(service, key);
        this.saveToStorage();
    }
    
    // 获取API密钥
    getApiKey(service) {
        return this.apiKeys.get(service);
    }
    
    // 保存到安全存储
    saveToStorage() {
        try {
            const config = {
                gemini_api_key: this.apiKeys.get('gemini')
            };
            const encrypted = this.encrypt(JSON.stringify(config));
            localStorage.setItem('secure_config', encrypted);
        } catch (error) {
            console.error('Failed to save secure config:', error);
        }
    }
    
    // 简单加密（生产环境应使用更强的加密）
    encrypt(text) {
        return btoa(text);
    }
    
    // 简单解密
    decrypt(encrypted) {
        return atob(encrypted);
    }
    
    // 清除所有密钥
    clearAll() {
        this.apiKeys.clear();
        localStorage.removeItem('secure_config');
    }
}

// 全局安全配置实例
window.secureConfig = new SecureConfig();
```

### 3. 性能配置

#### 缓存策略配置
```javascript
// 缓存配置
const CACHE_CONFIG = {
    // 静态资源缓存
    STATIC_CACHE: {
        VERSION: 'v1.0.0',
        RESOURCES: [
            '/',
            '/index.html',
            '/styles.css',
            '/app.js',
            '/config.js'
        ],
        MAX_AGE: 24 * 60 * 60 * 1000 // 24小时
    },
    
    // API响应缓存
    API_CACHE: {
        ENABLED: true,
        TTL: {
            USER_DATA: 5 * 60 * 1000,      // 5分钟
            SYSTEM_DATA: 30 * 60 * 1000,   // 30分钟
            AI_RESULTS: 60 * 60 * 1000      // 1小时
        },
        MAX_SIZE: 50 // 最大缓存条目数
    },
    
    // 图片缓存
    IMAGE_CACHE: {
        ENABLED: true,
        MAX_SIZE: 10 * 1024 * 1024, // 10MB
        COMPRESSION: {
            QUALITY: 0.8,
            MAX_WIDTH: 1920,
            MAX_HEIGHT: 1080
        }
    }
};
```

## 部署脚本

### 1. 自动化部署脚本

#### Bash部署脚本
```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e

# 配置变量
PROJECT_NAME="ota-order-processing"
DEPLOY_ENV=${1:-"staging"}
BUILD_DIR="./dist"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查必要的命令
    for cmd in rsync tar gzip; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要命令: $cmd"
            exit 1
        fi
    done
    
    log_info "依赖检查完成"
}

# 创建备份
create_backup() {
    if [ "$DEPLOY_ENV" = "production" ]; then
        log_info "创建生产环境备份..."
        
        mkdir -p "$BACKUP_DIR"
        BACKUP_NAME="${PROJECT_NAME}-$(date +'%Y%m%d-%H%M%S').tar.gz"
        
        if [ -d "/var/www/$PROJECT_NAME" ]; then
            tar -czf "$BACKUP_DIR/$BACKUP_NAME" -C "/var/www" "$PROJECT_NAME"
            log_info "备份已创建: $BACKUP_DIR/$BACKUP_NAME"
        else
            log_warn "生产目录不存在，跳过备份"
        fi
    fi
}

# 准备构建
prepare_build() {
    log_info "准备构建目录..."
    
    # 清理构建目录
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    
    # 复制源文件
    cp index.html "$BUILD_DIR/"
    cp styles.css "$BUILD_DIR/"
    cp app.js "$BUILD_DIR/"
    
    # 根据环境复制配置文件
    if [ "$DEPLOY_ENV" = "production" ]; then
        cp config.prod.js "$BUILD_DIR/config.js"
    else
        cp config.js "$BUILD_DIR/"
    fi
    
    log_info "构建目录准备完成"
}

# 优化文件
optimize_files() {
    log_info "优化文件..."
    
    # 压缩CSS（如果有工具）
    if command -v cleancss &> /dev/null; then
        cleancss -o "$BUILD_DIR/styles.css" "$BUILD_DIR/styles.css"
        log_info "CSS文件已压缩"
    fi
    
    # 压缩JavaScript（如果有工具）
    if command -v uglifyjs &> /dev/null; then
        uglifyjs "$BUILD_DIR/app.js" -o "$BUILD_DIR/app.js" -c -m
        log_info "JavaScript文件已压缩"
    fi
    
    # 添加版本号
    VERSION=$(date +'%Y%m%d%H%M%S')
    sed -i "s/{{VERSION}}/$VERSION/g" "$BUILD_DIR/index.html"
    
    log_info "文件优化完成"
}

# 部署到服务器
deploy_to_server() {
    log_info "部署到 $DEPLOY_ENV 环境..."
    
    case $DEPLOY_ENV in
        "production")
            DEPLOY_PATH="/var/www/$PROJECT_NAME"
            ;;
        "staging")
            DEPLOY_PATH="/var/www/staging-$PROJECT_NAME"
            ;;
        *)
            log_error "未知的部署环境: $DEPLOY_ENV"
            exit 1
            ;;
    esac
    
    # 创建部署目录
    sudo mkdir -p "$DEPLOY_PATH"
    
    # 同步文件
    sudo rsync -av --delete "$BUILD_DIR/" "$DEPLOY_PATH/"
    
    # 设置权限
    sudo chown -R www-data:www-data "$DEPLOY_PATH"
    sudo chmod -R 644 "$DEPLOY_PATH"
    sudo find "$DEPLOY_PATH" -type d -exec chmod 755 {} \;
    
    log_info "部署完成: $DEPLOY_PATH"
}

# 验证部署
validate_deployment() {
    log_info "验证部署..."
    
    # 检查文件是否存在
    DEPLOY_PATH="/var/www/$PROJECT_NAME"
    if [ "$DEPLOY_ENV" = "staging" ]; then
        DEPLOY_PATH="/var/www/staging-$PROJECT_NAME"
    fi
    
    REQUIRED_FILES=("index.html" "styles.css" "app.js" "config.js")
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$DEPLOY_PATH/$file" ]; then
            log_error "缺少文件: $file"
            exit 1
        fi
    done
    
    # 检查文件权限
    if [ "$(stat -c '%U' "$DEPLOY_PATH")" != "www-data" ]; then
        log_error "文件权限不正确"
        exit 1
    fi
    
    log_info "部署验证通过"
}

# 清理
cleanup() {
    log_info "清理临时文件..."
    rm -rf "$BUILD_DIR"
    log_info "清理完成"
}

# 主函数
main() {
    log_info "开始部署 $PROJECT_NAME 到 $DEPLOY_ENV 环境"
    
    check_dependencies
    create_backup
    prepare_build
    optimize_files
    deploy_to_server
    validate_deployment
    cleanup
    
    log_info "部署成功完成！"
    
    if [ "$DEPLOY_ENV" = "production" ]; then
        log_info "生产环境访问地址: https://ota-order.yourdomain.com"
    else
        log_info "测试环境访问地址: https://staging-ota-order.yourdomain.com"
    fi
}

# 错误处理
trap 'log_error "部署失败，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main
```

#### PowerShell部署脚本（Windows）
```powershell
# deploy.ps1 - Windows PowerShell部署脚本

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "staging",
    
    [Parameter(Mandatory=$false)]
    [string]$DeployPath = "C:\inetpub\wwwroot\ota-order-processing"
)

# 配置变量
$ProjectName = "ota-order-processing"
$BuildDir = ".\dist"
$BackupDir = ".\backups"
$LogFile = ".\deploy.log"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

# 检查依赖
function Test-Dependencies {
    Write-Log "检查部署依赖..."
    
    # 检查PowerShell版本
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        Write-Log "需要PowerShell 5.0或更高版本" "ERROR"
        exit 1
    }
    
    # 检查IIS（如果部署到IIS）
    if ($Environment -eq "production") {
        $iisFeature = Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
        if ($iisFeature.State -ne "Enabled") {
            Write-Log "IIS未安装或未启用" "WARN"
        }
    }
    
    Write-Log "依赖检查完成"
}

# 创建备份
function New-Backup {
    if ($Environment -eq "production" -and (Test-Path $DeployPath)) {
        Write-Log "创建生产环境备份..."
        
        if (!(Test-Path $BackupDir)) {
            New-Item -ItemType Directory -Path $BackupDir -Force
        }
        
        $BackupName = "$ProjectName-$(Get-Date -Format 'yyyyMMdd-HHmmss').zip"
        $BackupPath = Join-Path $BackupDir $BackupName
        
        Compress-Archive -Path $DeployPath -DestinationPath $BackupPath -Force
        Write-Log "备份已创建: $BackupPath"
    }
}

# 准备构建
function Initialize-Build {
    Write-Log "准备构建目录..."
    
    # 清理构建目录
    if (Test-Path $BuildDir) {
        Remove-Item -Path $BuildDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $BuildDir -Force
    
    # 复制源文件
    Copy-Item -Path "index.html" -Destination $BuildDir
    Copy-Item -Path "styles.css" -Destination $BuildDir
    Copy-Item -Path "app.js" -Destination $BuildDir
    
    # 根据环境复制配置文件
    if ($Environment -eq "production") {
        if (Test-Path "config.prod.js") {
            Copy-Item -Path "config.prod.js" -Destination "$BuildDir\config.js"
        } else {
            Copy-Item -Path "config.js" -Destination $BuildDir
        }
    } else {
        Copy-Item -Path "config.js" -Destination $BuildDir
    }
    
    Write-Log "构建目录准备完成"
}

# 部署到服务器
function Deploy-ToServer {
    Write-Log "部署到 $Environment 环境..."
    
    # 确保部署目录存在
    if (!(Test-Path $DeployPath)) {
        New-Item -ItemType Directory -Path $DeployPath -Force
    }
    
    # 复制文件
    Copy-Item -Path "$BuildDir\*" -Destination $DeployPath -Recurse -Force
    
    # 设置IIS权限（如果适用）
    if ($Environment -eq "production") {
        try {
            $acl = Get-Acl $DeployPath
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl -Path $DeployPath -AclObject $acl
            Write-Log "IIS权限设置完成"
        } catch {
            Write-Log "设置IIS权限失败: $($_.Exception.Message)" "WARN"
        }
    }
    
    Write-Log "部署完成: $DeployPath"
}

# 验证部署
function Test-Deployment {
    Write-Log "验证部署..."
    
    $RequiredFiles = @("index.html", "styles.css", "app.js", "config.js")
    
    foreach ($file in $RequiredFiles) {
        $filePath = Join-Path $DeployPath $file
        if (!(Test-Path $filePath)) {
            Write-Log "缺少文件: $file" "ERROR"
            exit 1
        }
    }
    
    Write-Log "部署验证通过"
}

# 清理
function Remove-TempFiles {
    Write-Log "清理临时文件..."
    if (Test-Path $BuildDir) {
        Remove-Item -Path $BuildDir -Recurse -Force
    }
    Write-Log "清理完成"
}

# 主函数
function Main {
    Write-Log "开始部署 $ProjectName 到 $Environment 环境"
    
    try {
        Test-Dependencies
        New-Backup
        Initialize-Build
        Deploy-ToServer
        Test-Deployment
        Remove-TempFiles
        
        Write-Log "部署成功完成！"
        
        if ($Environment -eq "production") {
            Write-Log "生产环境访问地址: https://ota-order.yourdomain.com"
        } else {
            Write-Log "测试环境访问地址: https://staging-ota-order.yourdomain.com"
        }
    } catch {
        Write-Log "部署失败: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# 执行主函数
Main
```

### 2. CI/CD配置

#### GitHub Actions工作流
```yaml
# .github/workflows/deploy.yml
name: Deploy OTA Order Processing System

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Install dependencies
      run: |
        npm install -g html-validate
        npm install -g stylelint
        npm install -g eslint
    
    - name: Validate HTML
      run: html-validate index.html
    
    - name: Lint CSS
      run: stylelint styles.css
    
    - name: Lint JavaScript
      run: eslint app.js config.js
    
    - name: Run security scan
      run: |
        # 检查敏感信息
        if grep -r "api_key\|password\|secret" *.js *.html; then
          echo "发现可能的敏感信息泄露"
          exit 1
        fi
  
  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to staging
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./
        publish_branch: gh-pages-staging
        cname: staging-ota-order.yourdomain.com
  
  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Create production config
      run: |
        cp config.js config.prod.js
        sed -i 's/ENVIRONMENT: "development"/ENVIRONMENT: "production"/' config.prod.js
        sed -i 's/DEBUG: { ENABLED: true/DEBUG: { ENABLED: false/' config.prod.js
    
    - name: Deploy to production
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./
        publish_branch: gh-pages
        cname: ota-order.yourdomain.com
    
    - name: Notify deployment
      run: |
        echo "生产环境部署完成"
        echo "访问地址: https://ota-order.yourdomain.com"
```

## 监控和维护

### 1. 性能监控

#### Web Vitals监控
```javascript
// 性能监控脚本
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.initializeMonitoring();
    }
    
    initializeMonitoring() {
        // 监控Core Web Vitals
        this.observeWebVitals();
        
        // 监控资源加载
        this.observeResourceTiming();
        
        // 监控用户交互
        this.observeUserInteractions();
        
        // 定期发送数据
        setInterval(() => this.sendMetrics(), 60000);
    }
    
    observeWebVitals() {
        // First Contentful Paint
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.name === 'first-contentful-paint') {
                    this.metrics.fcp = entry.startTime;
                }
            }
        }).observe({ entryTypes: ['paint'] });
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.metrics.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                this.metrics.fid = entry.processingStart - entry.startTime;
            }
        }).observe({ entryTypes: ['first-input'] });
        
        // Cumulative Layout Shift
        let clsValue = 0;
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            this.metrics.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
    }
    
    observeResourceTiming() {
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.initiatorType === 'fetch') {
                    const duration = entry.responseEnd - entry.requestStart;
                    this.recordApiTiming(entry.name, duration);
                }
            }
        }).observe({ entryTypes: ['resource'] });
    }
    
    observeUserInteractions() {
        // 监控按钮点击
        document.addEventListener('click', (event) => {
            if (event.target.tagName === 'BUTTON') {
                this.recordUserAction('button_click', event.target.id);
            }
        });
        
        // 监控表单提交
        document.addEventListener('submit', (event) => {
            this.recordUserAction('form_submit', event.target.id);
        });
    }
    
    recordApiTiming(url, duration) {
        if (!this.metrics.apiTimings) {
            this.metrics.apiTimings = [];
        }
        
        this.metrics.apiTimings.push({
            url: url,
            duration: duration,
            timestamp: Date.now()
        });
    }
    
    recordUserAction(action, element) {
        if (!this.metrics.userActions) {
            this.metrics.userActions = [];
        }
        
        this.metrics.userActions.push({
            action: action,
            element: element,
            timestamp: Date.now()
        });
    }
    
    sendMetrics() {
        if (Object.keys(this.metrics).length === 0) return;
        
        // 发送到分析服务（示例）
        if (CONFIG.FEATURES.ANALYTICS) {
            fetch('/api/analytics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    metrics: this.metrics,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            }).catch(error => {
                console.warn('Failed to send metrics:', error);
            });
        }
        
        // 清空已发送的指标
        this.metrics = {};
    }
}

// 初始化性能监控
if (CONFIG.FEATURES.ANALYTICS) {
    new PerformanceMonitor();
}
```

### 2. 错误监控

#### 错误收集和报告
```javascript
// 错误监控脚本
class ErrorMonitor {
    constructor() {
        this.errors = [];
        this.initializeErrorHandling();
    }
    
    initializeErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            this.recordError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: Date.now()
            });
        });
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError({
                type: 'promise',
                message: event.reason.message || event.reason,
                stack: event.reason.stack,
                timestamp: Date.now()
            });
        });
        
        // 资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.recordError({
                    type: 'resource',
                    message: `Failed to load ${event.target.tagName}: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    timestamp: Date.now()
                });
            }
        }, true);
        
        // 定期发送错误报告
        setInterval(() => this.sendErrors(), 30000);
    }
    
    recordError(error) {
        // 添加用户和环境信息
        error.userAgent = navigator.userAgent;
        error.url = window.location.href;
        error.userId = localStorage.getItem('user_id');
        error.sessionId = this.getSessionId();
        
        this.errors.push(error);
        
        // 立即发送严重错误
        if (this.isCriticalError(error)) {
            this.sendErrors();
        }
        
        // 控制台输出（开发环境）
        if (CONFIG.DEBUG.ENABLED) {
            console.error('Error recorded:', error);
        }
    }
    
    isCriticalError(error) {
        const criticalPatterns = [
            /network error/i,
            /api.*failed/i,
            /authentication.*failed/i,
            /cannot read property/i
        ];
        
        return criticalPatterns.some(pattern => 
            pattern.test(error.message)
        );
    }
    
    getSessionId() {
        let sessionId = sessionStorage.getItem('session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('session_id', sessionId);
        }
        return sessionId;
    }
    
    sendErrors() {
        if (this.errors.length === 0) return;
        
        const errorsToSend = [...this.errors];
        this.errors = [];
        
        // 发送到错误收集服务
        fetch('/api/errors', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                errors: errorsToSend,
                environment: CONFIG.ENVIRONMENT,
                version: CONFIG.VERSION
            })
        }).catch(error => {
            // 发送失败，重新加入队列
            this.errors.unshift(...errorsToSend);
            console.warn('Failed to send error report:', error);
        });
    }
}

// 初始化错误监控
new ErrorMonitor();
```

### 3. 健康检查

#### 系统健康检查脚本
```javascript
// 健康检查脚本
class HealthChecker {
    constructor() {
        this.checks = new Map();
        this.initializeChecks();
        this.startPeriodicChecks();
    }
    
    initializeChecks() {
        // API连通性检查
        this.addCheck('api_connectivity', async () => {
            try {
                const response = await fetch(CONFIG.GOMYHIRE_API.BASE_URL + '/health', {
                    method: 'GET',
                    timeout: 5000
                });
                return response.ok;
            } catch (error) {
                return false;
            }
        });
        
        // Gemini AI可用性检查
        this.addCheck('gemini_availability', async () => {
            try {
                const apiKey = window.secureConfig?.getApiKey('gemini');
                if (!apiKey) return false;
                
                const response = await fetch(
                    `${CONFIG.GEMINI_AI.BASE_URL}/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
                    {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            contents: [{ parts: [{ text: 'test' }] }]
                        }),
                        timeout: 10000
                    }
                );
                return response.ok;
            } catch (error) {
                return false;
            }
        });
        
        // 本地存储可用性检查
        this.addCheck('local_storage', () => {
            try {
                const testKey = 'health_check_test';
                localStorage.setItem(testKey, 'test');
                const value = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);
                return value === 'test';
            } catch (error) {
                return false;
            }
        });
        
        // 内存使用检查
        this.addCheck('memory_usage', () => {
            if ('memory' in performance) {
                const memory = performance.memory;
                const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                return usageRatio < 0.9; // 内存使用率低于90%
            }
            return true; // 无法检测时假设正常
        });
    }
    
    addCheck(name, checkFunction) {
        this.checks.set(name, checkFunction);
    }
    
    async runCheck(name) {
        const checkFunction = this.checks.get(name);
        if (!checkFunction) {
            throw new Error(`Health check '${name}' not found`);
        }
        
        try {
            const result = await checkFunction();
            return {
                name: name,
                status: result ? 'healthy' : 'unhealthy',
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                name: name,
                status: 'error',
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
    
    async runAllChecks() {
        const results = [];
        
        for (const [name] of this.checks) {
            const result = await this.runCheck(name);
            results.push(result);
        }
        
        return {
            overall: results.every(r => r.status === 'healthy') ? 'healthy' : 'unhealthy',
            checks: results,
            timestamp: Date.now()
        };
    }
    
    startPeriodicChecks() {
        // 每5分钟运行一次健康检查
        setInterval(async () => {
            const results = await this.runAllChecks();
            
            if (results.overall === 'unhealthy') {
                console.warn('Health check failed:', results);
                
                // 发送警报（如果配置了）
                this.sendAlert(results);
            }
            
            // 存储结果
            this.storeResults(results);
        }, 5 * 60 * 1000);
    }
    
    sendAlert(results) {
        // 发送警报到监控系统
        if (CONFIG.FEATURES.ANALYTICS) {
            fetch('/api/alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: 'health_check_failure',
                    results: results,
                    environment: CONFIG.ENVIRONMENT
                })
            }).catch(error => {
                console.error('Failed to send alert:', error);
            });
        }
    }
    
    storeResults(results) {
        try {
            const healthHistory = JSON.parse(
                localStorage.getItem('health_history') || '[]'
            );
            
            healthHistory.push(results);
            
            // 只保留最近24小时的记录
            const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
            const recentHistory = healthHistory.filter(
                record => record.timestamp > oneDayAgo
            );
            
            localStorage.setItem('health_history', JSON.stringify(recentHistory));
        } catch (error) {
            console.warn('Failed to store health check results:', error);
        }
    }
    
    getHealthHistory() {
        try {
            return JSON.parse(localStorage.getItem('health_history') || '[]');
        } catch (error) {
            return [];
        }
    }
}

// 初始化健康检查
const healthChecker = new HealthChecker();

// 暴露全局接口
window.getSystemHealth = () => healthChecker.runAllChecks();
window.getHealthHistory = () => healthChecker.getHealthHistory();
```

这份部署指南详细描述了OTA订单处理系统的完整部署流程，包括本地开发、生产环境部署、配置管理、自动化脚本、CI/CD集成以及监控维护，为系统的稳定运行提供了全面的部署支持。