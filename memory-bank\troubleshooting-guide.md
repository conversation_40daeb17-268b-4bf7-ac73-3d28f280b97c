# 故障排除指南 - OTA订单处理系统

## 故障排除概述

本指南提供了OTA订单处理系统常见问题的诊断方法和解决方案。按照问题类型分类，包含详细的排查步骤和修复建议。

## 快速诊断工具

### 1. 系统状态检查

#### 浏览器控制台检查
```javascript
// 在浏览器控制台运行以下命令进行快速诊断

// 1. 检查系统配置
console.log('系统配置:', CONFIG);

// 2. 检查认证状态
console.log('认证状态:', localStorage.getItem('auth_token') ? '已登录' : '未登录');

// 3. 检查API连通性
fetch(CONFIG.GOMYHIRE_API.BASE_URL + '/health')
  .then(response => console.log('API状态:', response.ok ? '正常' : '异常'))
  .catch(error => console.log('API错误:', error));

// 4. 检查本地存储
console.log('本地存储使用情况:', {
  used: JSON.stringify(localStorage).length,
  available: 5 * 1024 * 1024, // 5MB
  items: Object.keys(localStorage)
});

// 5. 检查系统健康状态（如果可用）
if (typeof getSystemHealth === 'function') {
  getSystemHealth().then(health => console.log('系统健康状态:', health));
}
```

#### 网络连接检查
```javascript
// 网络连接诊断脚本
function diagnoseNetwork() {
    const tests = [
        {
            name: 'GoMyHire API',
            url: CONFIG.GOMYHIRE_API.BASE_URL,
            timeout: 5000
        },
        {
            name: 'Gemini AI API',
            url: 'https://generativelanguage.googleapis.com',
            timeout: 5000
        },
        {
            name: 'Google DNS',
            url: 'https://*******',
            timeout: 3000
        }
    ];
    
    tests.forEach(async (test) => {
        try {
            const start = Date.now();
            const response = await fetch(test.url, {
                method: 'HEAD',
                mode: 'no-cors',
                signal: AbortSignal.timeout(test.timeout)
            });
            const duration = Date.now() - start;
            console.log(`${test.name}: 连接正常 (${duration}ms)`);
        } catch (error) {
            console.error(`${test.name}: 连接失败`, error.message);
        }
    });
}

// 运行网络诊断
diagnoseNetwork();
```

### 2. 错误日志收集

#### 客户端错误收集
```javascript
// 收集客户端错误信息
function collectErrorInfo() {
    const errorInfo = {
        // 浏览器信息
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        
        // 页面信息
        url: window.location.href,
        referrer: document.referrer,
        title: document.title,
        
        // 屏幕信息
        screenResolution: `${screen.width}x${screen.height}`,
        viewportSize: `${window.innerWidth}x${window.innerHeight}`,
        colorDepth: screen.colorDepth,
        
        // 性能信息
        memory: performance.memory ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
        } : null,
        
        // 本地存储信息
        localStorage: {
            available: typeof Storage !== 'undefined',
            used: localStorage ? JSON.stringify(localStorage).length : 0,
            keys: localStorage ? Object.keys(localStorage) : []
        },
        
        // 系统配置
        config: CONFIG,
        
        // 时间戳
        timestamp: new Date().toISOString()
    };
    
    console.log('错误诊断信息:', errorInfo);
    return errorInfo;
}

// 导出错误信息
function exportErrorInfo() {
    const errorInfo = collectErrorInfo();
    const blob = new Blob([JSON.stringify(errorInfo, null, 2)], {
        type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-info-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
}
```

## 常见问题分类

### 1. 登录和认证问题

#### 问题1.1: 无法登录系统

**症状**:
- 输入正确的用户名密码后仍然无法登录
- 显示"登录失败"错误信息
- 页面一直显示加载状态

**可能原因**:
1. API服务器不可用
2. 网络连接问题
3. 用户凭据已过期
4. CORS配置问题
5. 浏览器缓存问题

**诊断步骤**:
```javascript
// 1. 检查API连通性
fetch(CONFIG.GOMYHIRE_API.BASE_URL + '/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'test'
    })
})
.then(response => {
    console.log('API响应状态:', response.status);
    return response.text();
})
.then(data => console.log('API响应内容:', data))
.catch(error => console.error('API请求失败:', error));

// 2. 检查网络状态
console.log('网络状态:', navigator.onLine ? '在线' : '离线');

// 3. 检查CORS
fetch(CONFIG.GOMYHIRE_API.BASE_URL, { method: 'OPTIONS' })
.then(response => console.log('CORS检查:', response.headers.get('Access-Control-Allow-Origin')))
.catch(error => console.error('CORS错误:', error));
```

**解决方案**:
1. **API服务器问题**:
   - 联系系统管理员确认API服务状态
   - 检查API服务器日志
   - 验证API端点URL是否正确

2. **网络连接问题**:
   - 检查网络连接
   - 尝试访问其他网站确认网络正常
   - 检查防火墙设置
   - 尝试使用VPN或更换网络

3. **浏览器缓存问题**:
   ```javascript
   // 清除相关缓存
   localStorage.clear();
   sessionStorage.clear();
   
   // 强制刷新页面
   location.reload(true);
   ```

4. **CORS配置问题**:
   - 确认服务器CORS配置正确
   - 检查请求头设置
   - 联系后端开发人员

#### 问题1.2: 登录状态丢失

**症状**:
- 刷新页面后需要重新登录
- 操作过程中突然退出登录
- 认证token失效

**可能原因**:
1. Token过期时间设置过短
2. 本地存储被清除
3. 浏览器隐私模式
4. 多标签页冲突

**诊断步骤**:
```javascript
// 检查token状态
const token = localStorage.getItem('auth_token');
const tokenExpiry = localStorage.getItem('token_expiry');

console.log('Token存在:', !!token);
console.log('Token过期时间:', new Date(parseInt(tokenExpiry)));
console.log('当前时间:', new Date());
console.log('Token是否过期:', Date.now() > parseInt(tokenExpiry));

// 检查存储事件
window.addEventListener('storage', (e) => {
    console.log('存储变化:', e.key, e.oldValue, e.newValue);
});
```

**解决方案**:
1. **延长token有效期**:
   ```javascript
   // 在config.js中调整token过期时间
   const CONFIG = {
       AUTH: {
           TOKEN_EXPIRY: 8 * 60 * 60 * 1000, // 8小时
           REFRESH_THRESHOLD: 30 * 60 * 1000 // 30分钟前刷新
       }
   };
   ```

2. **实现token自动刷新**:
   ```javascript
   // 添加token刷新机制
   function setupTokenRefresh() {
       setInterval(() => {
           const tokenExpiry = localStorage.getItem('token_expiry');
           const refreshThreshold = CONFIG.AUTH.REFRESH_THRESHOLD;
           
           if (tokenExpiry && Date.now() > (parseInt(tokenExpiry) - refreshThreshold)) {
               refreshAuthToken();
           }
       }, 60000); // 每分钟检查一次
   }
   
   async function refreshAuthToken() {
       try {
           const response = await fetch(CONFIG.GOMYHIRE_API.BASE_URL + '/auth/refresh', {
               method: 'POST',
               headers: {
                   'Authorization': 'Bearer ' + localStorage.getItem('auth_token')
               }
           });
           
           if (response.ok) {
               const data = await response.json();
               localStorage.setItem('auth_token', data.token);
               localStorage.setItem('token_expiry', Date.now() + CONFIG.AUTH.TOKEN_EXPIRY);
           }
       } catch (error) {
           console.error('Token刷新失败:', error);
       }
   }
   ```

### 2. 图片上传和处理问题

#### 问题2.1: 图片上传失败

**症状**:
- 选择图片后无反应
- 显示"上传失败"错误
- 图片预览不显示

**可能原因**:
1. 文件格式不支持
2. 文件大小超限
3. 浏览器兼容性问题
4. 内存不足

**诊断步骤**:
```javascript
// 检查文件信息
function diagnoseFileUpload(file) {
    console.log('文件信息:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified)
    });
    
    // 检查文件类型
    const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'];
    console.log('文件类型支持:', supportedTypes.includes(file.type));
    
    // 检查文件大小
    const maxSize = 20 * 1024 * 1024; // 20MB
    console.log('文件大小检查:', file.size <= maxSize);
    
    // 检查浏览器支持
    console.log('FileReader支持:', typeof FileReader !== 'undefined');
    console.log('URL.createObjectURL支持:', typeof URL.createObjectURL !== 'undefined');
}

// 测试图片读取
function testImageReading(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        console.log('图片读取成功, 大小:', e.target.result.length);
        
        // 创建图片元素测试
        const img = new Image();
        img.onload = () => console.log('图片加载成功:', img.width, 'x', img.height);
        img.onerror = () => console.error('图片加载失败');
        img.src = e.target.result;
    };
    
    reader.onerror = function(e) {
        console.error('图片读取失败:', e);
    };
    
    reader.readAsDataURL(file);
}
```

**解决方案**:
1. **文件格式问题**:
   ```javascript
   // 添加文件格式验证
   function validateFileType(file) {
       const supportedTypes = [
           'image/jpeg', 'image/jpg', 'image/png', 
           'image/webp', 'image/heic', 'image/heif'
       ];
       
       if (!supportedTypes.includes(file.type)) {
           // 尝试通过文件扩展名判断
           const extension = file.name.split('.').pop().toLowerCase();
           const supportedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'heic', 'heif'];
           
           if (!supportedExtensions.includes(extension)) {
               throw new Error(`不支持的文件格式: ${file.type || extension}`);
           }
       }
       
       return true;
   }
   ```

2. **文件大小问题**:
   ```javascript
   // 添加文件大小检查和压缩
   async function handleLargeImage(file) {
       const maxSize = 20 * 1024 * 1024; // 20MB
       
       if (file.size > maxSize) {
           console.log('文件过大，尝试压缩...');
           return await compressImage(file, 0.8);
       }
       
       return file;
   }
   
   function compressImage(file, quality = 0.8) {
       return new Promise((resolve) => {
           const canvas = document.createElement('canvas');
           const ctx = canvas.getContext('2d');
           const img = new Image();
           
           img.onload = () => {
               // 计算压缩后的尺寸
               const maxWidth = 1920;
               const maxHeight = 1080;
               let { width, height } = img;
               
               if (width > maxWidth || height > maxHeight) {
                   const ratio = Math.min(maxWidth / width, maxHeight / height);
                   width *= ratio;
                   height *= ratio;
               }
               
               canvas.width = width;
               canvas.height = height;
               
               // 绘制压缩后的图片
               ctx.drawImage(img, 0, 0, width, height);
               
               // 转换为Blob
               canvas.toBlob(resolve, 'image/jpeg', quality);
           };
           
           img.src = URL.createObjectURL(file);
       });
   }
   ```

#### 问题2.2: AI图片处理失败

**症状**:
- 图片上传成功但AI处理失败
- 显示"图片识别失败"错误
- 处理时间过长超时

**可能原因**:
1. Gemini API密钥无效
2. API配额用尽
3. 图片内容无法识别
4. 网络超时

**诊断步骤**:
```javascript
// 测试Gemini API连接
async function testGeminiAPI() {
    const apiKey = window.secureConfig?.getApiKey('gemini');
    
    if (!apiKey) {
        console.error('Gemini API密钥未设置');
        return;
    }
    
    try {
        const response = await fetch(
            `${CONFIG.GEMINI_AI.BASE_URL}/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: 'Hello, this is a test.' }]
                    }]
                })
            }
        );
        
        console.log('Gemini API状态:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Gemini API错误:', errorText);
        } else {
            const data = await response.json();
            console.log('Gemini API响应:', data);
        }
    } catch (error) {
        console.error('Gemini API请求失败:', error);
    }
}

// 测试图片处理
async function testImageProcessing(imageFile) {
    const apiKey = window.secureConfig?.getApiKey('gemini');
    
    if (!apiKey) {
        console.error('API密钥未设置');
        return;
    }
    
    try {
        // 转换图片为base64
        const base64 = await fileToBase64(imageFile);
        const base64Data = base64.split(',')[1];
        
        const response = await fetch(
            `${CONFIG.GEMINI_AI.BASE_URL}/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [
                            { text: '请识别这张图片中的文字内容' },
                            {
                                inline_data: {
                                    mime_type: imageFile.type,
                                    data: base64Data
                                }
                            }
                        ]
                    }]
                })
            }
        );
        
        console.log('图片处理响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('图片处理结果:', data);
        } else {
            const errorText = await response.text();
            console.error('图片处理失败:', errorText);
        }
    } catch (error) {
        console.error('图片处理错误:', error);
    }
}

function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}
```

**解决方案**:
1. **API密钥问题**:
   ```javascript
   // 验证和重新设置API密钥
   function validateAndSetApiKey() {
       const apiKey = prompt('请输入有效的Gemini API密钥:');
       
       if (apiKey && apiKey.trim()) {
           window.secureConfig.setApiKey('gemini', apiKey.trim());
           console.log('API密钥已更新');
           
           // 测试新密钥
           testGeminiAPI();
       }
   }
   ```

2. **API配额问题**:
   - 检查Google Cloud Console中的API使用情况
   - 升级API配额或等待配额重置
   - 实现请求频率限制

3. **图片质量优化**:
   ```javascript
   // 图片预处理
   async function preprocessImage(file) {
       // 确保图片清晰度
       const processedFile = await enhanceImageQuality(file);
       
       // 检查图片尺寸
       const img = new Image();
       img.src = URL.createObjectURL(processedFile);
       
       await new Promise(resolve => {
           img.onload = resolve;
       });
       
       console.log('图片尺寸:', img.width, 'x', img.height);
       
       // 如果图片太小，建议用户重新拍摄
       if (img.width < 800 || img.height < 600) {
           console.warn('图片分辨率较低，可能影响识别效果');
       }
       
       return processedFile;
   }
   ```

### 3. AI处理问题

#### 问题3.1: AI处理结果不准确

**症状**:
- 提取的信息不正确
- 缺少重要信息
- 格式化错误

**可能原因**:
1. 输入内容不清晰
2. AI提示词不够精确
3. 数据格式不标准
4. 语言识别错误

**诊断步骤**:
```javascript
// 分析AI处理结果
function analyzeAIResult(input, output) {
    console.log('输入内容长度:', input.length);
    console.log('输入内容预览:', input.substring(0, 200));
    console.log('输出结果:', output);
    
    // 检查必填字段
    const requiredFields = [
        'customer_name', 'customer_contact', 'pickup_date',
        'pickup_time', 'pickup_location', 'destination'
    ];
    
    const missingFields = requiredFields.filter(field => 
        !output[field] || output[field].trim() === ''
    );
    
    if (missingFields.length > 0) {
        console.warn('缺少必填字段:', missingFields);
    }
    
    // 检查数据格式
    if (output.pickup_date && !isValidDate(output.pickup_date)) {
        console.warn('日期格式错误:', output.pickup_date);
    }
    
    if (output.pickup_time && !isValidTime(output.pickup_time)) {
        console.warn('时间格式错误:', output.pickup_time);
    }
    
    if (output.customer_contact && !isValidPhone(output.customer_contact)) {
        console.warn('电话格式可能错误:', output.customer_contact);
    }
}

function isValidDate(dateStr) {
    const date = new Date(dateStr);
    return date instanceof Date && !isNaN(date);
}

function isValidTime(timeStr) {
    return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeStr);
}

function isValidPhone(phoneStr) {
    return /^\+?[1-9]\d{1,14}$/.test(phoneStr.replace(/[\s-()]/g, ''));
}
```

**解决方案**:
1. **优化AI提示词**:
   ```javascript
   // 改进的提示词模板
   const improvedPrompts = {
       chong_dealer: `
请仔细分析以下重庆经销商订单信息，提取关键数据：

输入内容：{input}

请按照以下要求提取信息：
1. 客户姓名：完整的中文姓名
2. 联系电话：包含国际区号的完整电话号码
3. 接机日期：转换为YYYY-MM-DD格式，"明天"转换为具体日期
4. 接机时间：航班到达时间+1小时，格式HH:MM
5. 接机地点：标准化的机场或地点名称
6. 目的地：标准化的酒店或地点名称
7. 乘客人数：数字
8. 特殊要求：其他备注信息

注意事项：
- 日期计算基于当前时间：{current_date}
- 电话号码添加+60前缀（如果没有国际区号）
- 地点名称使用英文标准名称
- 时间使用24小时制

请以JSON格式返回结果。
       `,
       
       auto_detect: `
请分析以下订单信息并提取关键数据：

输入内容：{input}

请识别订单类型并提取以下信息：
1. 订单类型：判断是否为重庆经销商订单
2. 客户信息：姓名、联系方式
3. 时间信息：日期、时间
4. 地点信息：出发地、目的地
5. 服务要求：人数、特殊需求

请以结构化JSON格式返回结果。
       `
   };
   ```

2. **添加结果验证和修正**:
   ```javascript
   // AI结果后处理
   function postProcessAIResult(result, originalInput) {
       const processed = { ...result };
       
       // 日期修正
       if (processed.pickup_date) {
           processed.pickup_date = normalizeDate(processed.pickup_date);
       }
       
       // 时间修正
       if (processed.pickup_time) {
           processed.pickup_time = normalizeTime(processed.pickup_time);
       }
       
       // 电话号码修正
       if (processed.customer_contact) {
           processed.customer_contact = normalizePhone(processed.customer_contact);
       }
       
       // 地点名称标准化
       if (processed.pickup_location) {
           processed.pickup_location = standardizeLocation(processed.pickup_location);
       }
       
       if (processed.destination) {
           processed.destination = standardizeLocation(processed.destination);
       }
       
       // 生成OTA参考号
       if (!processed.ota_reference_number) {
           processed.ota_reference_number = generateOTAReference();
       }
       
       return processed;
   }
   
   function normalizeDate(dateStr) {
       // 处理相对日期
       const today = new Date();
       const tomorrow = new Date(today);
       tomorrow.setDate(tomorrow.getDate() + 1);
       
       if (dateStr.includes('明天') || dateStr.includes('tomorrow')) {
           return tomorrow.toISOString().split('T')[0];
       }
       
       if (dateStr.includes('今天') || dateStr.includes('today')) {
           return today.toISOString().split('T')[0];
       }
       
       // 尝试解析其他日期格式
       const date = new Date(dateStr);
       if (isValidDate(date)) {
           return date.toISOString().split('T')[0];
       }
       
       return dateStr;
   }
   
   function normalizeTime(timeStr) {
       // 标准化时间格式
       const timeMatch = timeStr.match(/(\d{1,2})[:.](\d{2})/);
       if (timeMatch) {
           const hours = parseInt(timeMatch[1]);
           const minutes = parseInt(timeMatch[2]);
           return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
       }
       
       return timeStr;
   }
   
   function normalizePhone(phoneStr) {
       // 清理电话号码
       let cleaned = phoneStr.replace(/[\s-()]/g, '');
       
       // 添加国际区号
       if (!cleaned.startsWith('+')) {
           if (cleaned.startsWith('0')) {
               cleaned = '+60' + cleaned.substring(1);
           } else if (!cleaned.startsWith('60')) {
               cleaned = '+60' + cleaned;
           } else {
               cleaned = '+' + cleaned;
           }
       }
       
       return cleaned;
   }
   ```

#### 问题3.2: AI处理超时

**症状**:
- AI处理时间过长
- 请求超时错误
- 页面无响应

**可能原因**:
1. 网络连接慢
2. 输入内容过长
3. API服务器负载高
4. 超时设置过短

**解决方案**:
```javascript
// 实现重试机制和超时处理
class AIProcessor {
    constructor() {
        this.maxRetries = 3;
        this.baseTimeout = 30000; // 30秒
        this.retryDelay = 2000; // 2秒
    }
    
    async processWithRetry(input, type = 'auto_detect') {
        let lastError;
        
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                console.log(`AI处理尝试 ${attempt}/${this.maxRetries}`);
                
                const timeout = this.baseTimeout * attempt; // 递增超时时间
                const result = await this.processWithTimeout(input, type, timeout);
                
                console.log(`AI处理成功，尝试次数: ${attempt}`);
                return result;
                
            } catch (error) {
                lastError = error;
                console.warn(`AI处理失败，尝试 ${attempt}/${this.maxRetries}:`, error.message);
                
                if (attempt < this.maxRetries) {
                    console.log(`等待 ${this.retryDelay}ms 后重试...`);
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        throw new Error(`AI处理失败，已重试 ${this.maxRetries} 次。最后错误: ${lastError.message}`);
    }
    
    async processWithTimeout(input, type, timeout) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const result = await this.callGeminiAPI(input, type, controller.signal);
            clearTimeout(timeoutId);
            return result;
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error(`AI处理超时 (${timeout}ms)`);
            }
            
            throw error;
        }
    }
    
    async callGeminiAPI(input, type, signal) {
        const apiKey = window.secureConfig?.getApiKey('gemini');
        if (!apiKey) {
            throw new Error('Gemini API密钥未设置');
        }
        
        const prompt = this.buildPrompt(input, type);
        
        const response = await fetch(
            `${CONFIG.GEMINI_AI.BASE_URL}/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        temperature: 0.1,
                        topK: 1,
                        topP: 1,
                        maxOutputTokens: 2048
                    }
                }),
                signal: signal
            }
        );
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API请求失败: ${response.status} - ${errorText}`);
        }
        
        const data = await response.json();
        return this.parseAIResponse(data);
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    buildPrompt(input, type) {
        // 构建优化的提示词
        const currentDate = new Date().toISOString().split('T')[0];
        
        let prompt = improvedPrompts[type] || improvedPrompts.auto_detect;
        prompt = prompt.replace('{input}', input);
        prompt = prompt.replace('{current_date}', currentDate);
        
        return prompt;
    }
    
    parseAIResponse(data) {
        try {
            const content = data.candidates[0].content.parts[0].text;
            
            // 尝试解析JSON
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }
            
            throw new Error('AI响应中未找到有效的JSON数据');
        } catch (error) {
            throw new Error(`解析AI响应失败: ${error.message}`);
        }
    }
}

// 使用改进的AI处理器
const aiProcessor = new AIProcessor();
```

### 4. 订单创建问题

#### 问题4.1: 订单创建失败

**症状**:
- 点击创建订单后显示错误
- 订单数据提交失败
- 服务器返回错误响应

**可能原因**:
1. 必填字段缺失
2. 数据格式错误
3. 权限不足
4. 服务器错误

**诊断步骤**:
```javascript
// 验证订单数据
function validateOrderData(orderData) {
    const errors = [];
    
    // 检查必填字段
    const requiredFields = {
        'sub_category_id': '服务分类',
        'ota_reference_number': 'OTA参考号',
        'car_type_id': '车型',
        'incharge_by_backend_user_id': '负责人',
        'customer_name': '客户姓名',
        'customer_contact': '客户联系方式',
        'pickup_date': '接机日期',
        'pickup_time': '接机时间',
        'pickup_location': '接机地点',
        'destination': '目的地'
    };
    
    for (const [field, label] of Object.entries(requiredFields)) {
        if (!orderData[field] || orderData[field].toString().trim() === '') {
            errors.push(`${label}不能为空`);
        }
    }
    
    // 检查数据格式
    if (orderData.pickup_date && !isValidDate(orderData.pickup_date)) {
        errors.push('接机日期格式错误');
    }
    
    if (orderData.pickup_time && !isValidTime(orderData.pickup_time)) {
        errors.push('接机时间格式错误');
    }
    
    if (orderData.customer_contact && !isValidPhone(orderData.customer_contact)) {
        errors.push('客户联系方式格式错误');
    }
    
    // 检查日期是否为未来日期
    if (orderData.pickup_date) {
        const pickupDate = new Date(orderData.pickup_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (pickupDate < today) {
            errors.push('接机日期不能是过去的日期');
        }
    }
    
    return errors;
}

// 测试订单创建API
async function testOrderCreation(orderData) {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
        console.error('用户未登录');
        return;
    }
    
    try {
        console.log('订单数据:', orderData);
        
        const response = await fetch(CONFIG.GOMYHIRE_API.BASE_URL + '/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify(orderData)
        });
        
        console.log('API响应状态:', response.status);
        console.log('API响应头:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('API响应内容:', responseText);
        
        if (response.ok) {
            const data = JSON.parse(responseText);
            console.log('订单创建成功:', data);
        } else {
            console.error('订单创建失败:', responseText);
        }
    } catch (error) {
        console.error('订单创建请求失败:', error);
    }
}
```

**解决方案**:
1. **数据验证和修正**:
   ```javascript
   // 订单数据预处理
   function preprocessOrderData(rawData) {
       const processed = { ...rawData };
       
       // 确保必填字段有默认值
       if (!processed.sub_category_id) {
           processed.sub_category_id = 1; // 默认分类
       }
       
       if (!processed.car_type_id) {
           processed.car_type_id = 1; // 默认车型
       }
       
       if (!processed.incharge_by_backend_user_id) {
           const currentUser = JSON.parse(localStorage.getItem('current_user') || '{}');
           processed.incharge_by_backend_user_id = currentUser.id || 1;
       }
       
       if (!processed.ota_reference_number) {
           processed.ota_reference_number = generateOTAReference();
       }
       
       // 数据类型转换
       processed.sub_category_id = parseInt(processed.sub_category_id);
       processed.car_type_id = parseInt(processed.car_type_id);
       processed.incharge_by_backend_user_id = parseInt(processed.incharge_by_backend_user_id);
       
       if (processed.passenger_count) {
           processed.passenger_count = parseInt(processed.passenger_count) || 1;
       }
       
       return processed;
   }
   
   function generateOTAReference() {
       const timestamp = Date.now().toString(36);
       const random = Math.random().toString(36).substr(2, 5);
       return `OTA${timestamp}${random}`.toUpperCase();
   }
   ```

2. **错误处理和重试**:
   ```javascript
   // 改进的订单创建函数
   async function createOrderWithRetry(orderData, maxRetries = 3) {
       let lastError;
       
       for (let attempt = 1; attempt <= maxRetries; attempt++) {
           try {
               console.log(`订单创建尝试 ${attempt}/${maxRetries}`);
               
               // 验证数据
               const errors = validateOrderData(orderData);
               if (errors.length > 0) {
                   throw new Error('数据验证失败: ' + errors.join(', '));
               }
               
               // 预处理数据
               const processedData = preprocessOrderData(orderData);
               
               // 发送请求
               const result = await createOrder(processedData);
               
               console.log('订单创建成功:', result);
               return result;
               
           } catch (error) {
               lastError = error;
               console.warn(`订单创建失败，尝试 ${attempt}/${maxRetries}:`, error.message);
               
               // 如果是数据验证错误，不重试
               if (error.message.includes('数据验证失败')) {
                   throw error;
               }
               
               if (attempt < maxRetries) {
                   await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
               }
           }
       }
       
       throw new Error(`订单创建失败，已重试 ${maxRetries} 次。最后错误: ${lastError.message}`);
   }
   ```

### 5. 性能问题

#### 问题5.1: 页面加载缓慢

**症状**:
- 页面打开时间过长
- 资源加载缓慢
- 交互响应延迟

**诊断步骤**:
```javascript
// 性能诊断工具
function diagnosePerformance() {
    // 检查页面加载性能
    const navigation = performance.getEntriesByType('navigation')[0];
    console.log('页面加载性能:', {
        DNS查询: navigation.domainLookupEnd - navigation.domainLookupStart,
        TCP连接: navigation.connectEnd - navigation.connectStart,
        请求响应: navigation.responseEnd - navigation.requestStart,
        DOM解析: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        页面加载: navigation.loadEventEnd - navigation.navigationStart
    });
    
    // 检查资源加载
    const resources = performance.getEntriesByType('resource');
    console.log('资源加载统计:', {
        总数: resources.length,
        平均加载时间: resources.reduce((sum, r) => sum + r.duration, 0) / resources.length,
        最慢资源: resources.sort((a, b) => b.duration - a.duration).slice(0, 5)
    });
    
    // 检查内存使用
    if (performance.memory) {
        console.log('内存使用情况:', {
            已使用: (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
            总计: (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
            限制: (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + 'MB'
        });
    }
}

// 运行性能诊断
diagnosePerformance();
```

**解决方案**:
1. **资源优化**:
   ```javascript
   // 延迟加载非关键资源
   function lazyLoadResources() {
       // 延迟加载图片
       const images = document.querySelectorAll('img[data-src]');
       const imageObserver = new IntersectionObserver((entries) => {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   const img = entry.target;
                   img.src = img.dataset.src;
                   img.removeAttribute('data-src');
                   imageObserver.unobserve(img);
               }
           });
       });
       
       images.forEach(img => imageObserver.observe(img));
   }
   
   // 预加载关键资源
   function preloadCriticalResources() {
       const criticalResources = [
           '/styles.css',
           '/app.js',
           '/config.js'
       ];
       
       criticalResources.forEach(resource => {
           const link = document.createElement('link');
           link.rel = 'preload';
           link.href = resource;
           link.as = resource.endsWith('.css') ? 'style' : 'script';
           document.head.appendChild(link);
       });
   }
   ```

2. **缓存优化**:
   ```javascript
   // 实现智能缓存
   class SmartCache {
       constructor() {
           this.cache = new Map();
           this.maxSize = 50;
           this.ttl = 30 * 60 * 1000; // 30分钟
       }
       
       set(key, value, customTTL) {
           // 清理过期缓存
           this.cleanup();
           
           // 如果缓存已满，删除最旧的项
           if (this.cache.size >= this.maxSize) {
               const firstKey = this.cache.keys().next().value;
               this.cache.delete(firstKey);
           }
           
           this.cache.set(key, {
               value: value,
               timestamp: Date.now(),
               ttl: customTTL || this.ttl
           });
       }
       
       get(key) {
           const item = this.cache.get(key);
           
           if (!item) {
               return null;
           }
           
           // 检查是否过期
           if (Date.now() - item.timestamp > item.ttl) {
               this.cache.delete(key);
               return null;
           }
           
           return item.value;
       }
       
       cleanup() {
           const now = Date.now();
           for (const [key, item] of this.cache.entries()) {
               if (now - item.timestamp > item.ttl) {
                   this.cache.delete(key);
               }
           }
       }
   }
   
   // 全局缓存实例
   window.smartCache = new SmartCache();
   ```

## 联系支持

如果以上解决方案无法解决您的问题，请联系技术支持：

### 技术支持信息
- **邮箱**: <EMAIL>
- **电话**: +60-3-1234-5678
- **工作时间**: 周一至周五 9:00-18:00 (马来西亚时间)

### 提交问题时请包含以下信息
1. **问题描述**: 详细描述遇到的问题
2. **重现步骤**: 如何重现该问题
3. **环境信息**: 浏览器版本、操作系统、网络环境
4. **错误信息**: 控制台错误日志、截图
5. **诊断信息**: 运行诊断脚本的输出结果

### 紧急问题处理
对于影响业务运营的紧急问题：
- **紧急热线**: +60-3-1234-5679
- **24小时支持**: 发送邮件至 <EMAIL>
- **响应时间**: 2小时内响应，4小时内提供解决方案

这份故障排除指南涵盖了OTA订单处理系统的主要问题类型和解决方案，帮助用户快速诊断和解决常见问题，确保系统的稳定运行。