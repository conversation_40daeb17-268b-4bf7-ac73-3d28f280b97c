# 产品需求文档 - OTA订单处理系统

## 业务背景

### 行业背景
OTA（Online Travel Agency）行业需要处理大量的旅游订单，包括机票、酒店、接送机等服务。传统的人工处理方式效率低下，容易出错，需要智能化的解决方案。

### 业务痛点
1. **手工处理效率低**: 人工处理订单信息耗时长，容易出错
2. **信息格式不统一**: 不同渠道的订单格式差异大，难以标准化
3. **重复工作多**: 大量重复的数据录入和格式转换工作
4. **错误率高**: 人工处理容易出现日期、时间、地点等信息错误
5. **处理速度慢**: 无法快速响应客户需求

### 解决方案价值
- **提升效率**: AI自动处理，大幅减少人工时间
- **降低错误**: 智能识别和校正，减少人为错误
- **标准化处理**: 统一的输出格式，便于后续处理
- **快速响应**: 实时处理，提升客户满意度

## 目标用户

### 主要用户群体

#### 1. 订单处理专员
- **角色描述**: 负责日常OTA订单处理的工作人员
- **技能水平**: 熟悉旅游业务，基础计算机操作技能
- **使用场景**: 每日处理50-200个订单
- **核心需求**: 快速、准确地处理订单信息

#### 2. 业务主管
- **角色描述**: 负责订单处理团队管理的主管
- **技能水平**: 丰富的业务经验，管理技能
- **使用场景**: 监控处理质量，处理异常订单
- **核心需求**: 系统稳定性，处理结果可控

#### 3. 客服人员
- **角色描述**: 处理客户咨询和投诉的客服人员
- **技能水平**: 客服技能，基础业务知识
- **使用场景**: 协助处理特殊订单，回答客户问题
- **核心需求**: 快速查询订单状态，处理结果透明

### 用户画像

#### 典型用户：张小美（订单处理专员）
- **年龄**: 25-35岁
- **工作经验**: 2-5年旅游行业经验
- **技术水平**: 熟练使用Office软件，基础网页操作
- **工作环境**: 办公室，使用台式机或笔记本
- **工作压力**: 每日处理大量订单，时间紧迫
- **期望**: 系统操作简单，处理速度快，结果准确

## 功能需求

### 核心功能

#### 1. 用户认证系统
**功能描述**: 安全的用户登录和会话管理

**详细需求**:
- 用户名密码登录
- 登录状态持久化（24小时）
- 自动登出机制
- 登录失败提示

**验收标准**:
- 正确凭据能够成功登录
- 错误凭据显示明确错误信息
- 登录状态在浏览器关闭后保持
- 超时后自动登出

#### 2. 订单信息输入
**功能描述**: 支持多种方式输入订单信息

**2.1 文字输入**
- 大文本框支持粘贴订单信息
- 支持多行文本
- 实时字符计数
- 输入验证和提示

**2.2 图片上传**
- 支持拖拽上传
- 支持点击选择文件
- 多图片同时上传（最多10张）
- 支持格式：JPG, PNG, GIF, WebP
- 文件大小限制：10MB/张
- 图片预览功能
- 上传进度显示

**验收标准**:
- 文字输入框正常工作，支持大量文本
- 图片上传成功，显示预览
- 不支持的格式给出明确提示
- 超大文件给出大小限制提示

#### 3. OTA类型识别
**功能描述**: 智能识别或手动选择OTA类型

**详细需求**:
- 自动识别模式：AI分析内容自动判断
- 手动选择模式：用户选择特定OTA类型
- 支持的OTA类型：
  - Chong Dealer（重庆经销商）
  - 自动识别
  - 其他通用类型

**验收标准**:
- 自动识别准确率 > 85%
- 手动选择功能正常
- 不同类型使用对应的处理规则

#### 4. AI智能处理
**功能描述**: 使用Gemini AI处理订单信息

**4.1 文字处理**
- 提取关键信息（日期、时间、航班、酒店等）
- 日期智能修正（处理过期日期）
- 时间计算（接机/送机时间）
- 地点标准化
- 生成OTA参考号

**4.2 图片处理**
- 使用Gemini Vision API识别图片内容
- 提取图片中的文字信息
- 识别表格、表单结构
- 处理手写文字

**验收标准**:
- 关键信息提取准确率 > 90%
- 日期修正逻辑正确
- 时间计算符合业务规则
- 图片文字识别准确率 > 85%

#### 5. 结果预览与编辑
**功能描述**: 展示AI处理结果，支持手动编辑

**详细需求**:
- 结构化展示处理结果
- 支持字段级别编辑
- 实时验证编辑内容
- 批量订单展示
- 单个订单详细编辑
- 重新处理功能

**验收标准**:
- 结果展示清晰易读
- 编辑功能正常工作
- 验证规则有效
- 重新处理功能正常

#### 6. 订单创建
**功能描述**: 通过GoMyHire API创建订单

**详细需求**:
- 批量创建订单
- 实时状态反馈
- 错误处理和重试
- 成功/失败统计
- 详细错误信息展示

**验收标准**:
- API调用成功率 > 95%
- 错误信息准确明确
- 重试机制有效
- 状态反馈及时

### 辅助功能

#### 1. 数据导出
- 支持导出处理结果
- 多种格式：JSON, CSV
- 包含处理日志

#### 2. 系统设置
- API密钥配置
- 默认参数设置
- 界面主题选择

#### 3. 帮助文档
- 操作指南
- 常见问题
- 联系方式

## 业务规则

### OTA订单处理规则（基于Chong Dealer）

#### 1. 日期处理规则
- **过期日期修正**: 如果日期已过期，自动推算到下一年的对应日期
- **默认年份**: 处理结果默认为2025年
- **跨月处理**: 正确处理跨月跨年的日期
- **日期格式**: 统一输出为YYYY-MM-DD格式

#### 2. 时间计算规则
- **接机时间**: 使用航班到达时间
- **送机时间**: 航班起飞时间减去3.5小时
- **时间格式**: 24小时制，HH:MM格式

#### 3. 地点处理规则
- **接机**: pickup = "klia", drop = 酒店英文名
- **送机**: pickup = 酒店英文名, drop = "klia"
- **酒店名称**: 自动转换中文酒店名为标准英文名
- **地点标准化**: 统一地点名称格式

#### 4. 参考号生成规则
- **格式**: 日期时间-航班号-客人姓名
- **示例**: "202402141422-AK5747-余欣怡"
- **唯一性**: 确保每个订单的参考号唯一

#### 5. 联系方式处理
- **电话号码**: 提取并格式化电话号码
- **联系人**: 识别主要联系人姓名
- **备注信息**: 保留重要的备注信息

## 用户故事

### Epic 1: 订单信息输入

#### Story 1.1: 文字订单输入
**作为** 订单处理专员  
**我希望** 能够直接粘贴订单文字信息  
**以便** 快速输入订单内容  

**验收标准**:
- 给定一个包含订单信息的文本
- 当我粘贴到输入框中
- 那么系统应该正确接收并显示文本内容

#### Story 1.2: 图片订单上传
**作为** 订单处理专员  
**我希望** 能够上传订单截图或照片  
**以便** 处理图片格式的订单信息  

**验收标准**:
- 给定一张包含订单信息的图片
- 当我拖拽或选择上传图片
- 那么系统应该成功上传并显示图片预览

### Epic 2: AI智能处理

#### Story 2.1: 自动信息提取
**作为** 订单处理专员  
**我希望** AI能够自动提取订单中的关键信息  
**以便** 减少手工录入工作  

**验收标准**:
- 给定一个包含完整订单信息的输入
- 当我点击"处理订单"按钮
- 那么系统应该提取出日期、时间、航班、酒店、联系人等信息

#### Story 2.2: 智能日期修正
**作为** 订单处理专员  
**我希望** 系统能够自动修正过期的日期  
**以便** 避免日期错误导致的问题  

**验收标准**:
- 给定一个包含过期日期的订单
- 当AI处理订单时
- 那么系统应该将日期修正为未来的对应日期

### Epic 3: 结果管理

#### Story 3.1: 结果预览
**作为** 订单处理专员  
**我希望** 能够预览AI处理的结果  
**以便** 确认信息的准确性  

**验收标准**:
- 给定AI已处理的订单信息
- 当处理完成后
- 那么系统应该清晰地展示所有提取的信息

#### Story 3.2: 手动编辑
**作为** 订单处理专员  
**我希望** 能够手动编辑AI处理的结果  
**以便** 修正任何错误或遗漏  

**验收标准**:
- 给定一个AI处理的结果
- 当我点击编辑按钮
- 那么我应该能够修改任何字段的值

### Epic 4: 订单创建

#### Story 4.1: 批量创建订单
**作为** 订单处理专员  
**我希望** 能够批量创建多个订单  
**以便** 提高处理效率  

**验收标准**:
- 给定多个已确认的订单信息
- 当我点击"创建订单"按钮
- 那么系统应该逐个调用API创建订单

#### Story 4.2: 状态反馈
**作为** 订单处理专员  
**我希望** 能够看到每个订单的创建状态  
**以便** 了解处理进度和结果  

**验收标准**:
- 给定正在创建的订单
- 当创建过程进行时
- 那么系统应该实时显示每个订单的状态（成功/失败/进行中）

## 非功能性需求

### 性能需求
- **页面加载时间**: < 3秒
- **AI处理时间**: < 30秒/订单
- **API响应时间**: < 5秒
- **图片上传时间**: < 10秒/张

### 兼容性需求
- **浏览器支持**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **屏幕分辨率**: 1024x768及以上
- **移动设备**: 基础支持，主要针对桌面端

### 可用性需求
- **界面语言**: 中文
- **操作复杂度**: 新用户5分钟内上手
- **错误恢复**: 所有错误都有明确提示和恢复方案
- **帮助文档**: 完整的操作指南

### 安全需求
- **数据传输**: HTTPS加密
- **API密钥**: 安全存储，不暴露在代码中
- **会话管理**: 安全的登录状态管理
- **数据清理**: 敏感信息不在本地永久存储

### 可靠性需求
- **系统可用性**: 99%+
- **错误处理**: 所有异常都有处理机制
- **数据一致性**: 处理结果准确可靠
- **恢复能力**: 网络中断后能够恢复

## 约束条件

### 技术约束
- 纯前端实现，不使用后端服务器
- 不使用npm或其他包管理工具
- 不引入大型第三方库
- 必须支持本地文件运行

### 业务约束
- 必须与现有GoMyHire API兼容
- 处理规则必须符合Chong Dealer要求
- 不能存储客户敏感信息
- 必须支持批量处理

### 资源约束
- 开发时间有限
- 无专门的后端开发资源
- 依赖第三方AI服务
- 受API调用频率限制

## 成功指标

### 功能指标
- AI处理准确率 > 90%
- 订单创建成功率 > 95%
- 用户操作成功率 > 98%

### 效率指标
- 单个订单处理时间 < 2分钟
- 批量处理效率提升 > 80%
- 人工干预率 < 20%

### 用户满意度指标
- 用户满意度 > 4.5/5
- 系统易用性评分 > 4.0/5
- 错误率投诉 < 5%

## 风险评估

### 高风险
1. **AI服务不稳定**: Gemini API可能出现服务中断
2. **处理准确率不达标**: AI识别错误率过高
3. **API兼容性问题**: GoMyHire API变更导致不兼容

### 中风险
1. **浏览器兼容性**: 不同浏览器表现不一致
2. **网络依赖**: 网络问题影响使用
3. **用户接受度**: 用户不适应新系统

### 低风险
1. **性能问题**: 处理速度不够快
2. **界面问题**: UI/UX需要优化
3. **文档不完善**: 使用说明不够详细

### 风险缓解措施
1. **备用方案**: 准备手动处理流程
2. **错误处理**: 完善的异常处理机制
3. **用户培训**: 提供详细的使用指南
4. **监控机制**: 实时监控系统状态
5. **快速响应**: 建立问题反馈和处理机制