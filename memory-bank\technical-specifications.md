# 技术规范文档 - OTA订单处理系统

## 技术栈概述

### 核心技术
- **前端框架**: 原生JavaScript (ES6+)
- **样式框架**: 原生CSS3 (Flexbox + Grid)
- **AI服务**: Google Gemini API
- **后端API**: GoMyHire REST API
- **存储方案**: LocalStorage
- **部署方式**: 静态文件部署

### 浏览器兼容性
- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **移动浏览器**: iOS Safari 13+, Chrome Mobile 80+
- **不支持**: Internet Explorer

## 文件结构规范

### 项目目录结构
```
create-job/
├── index.html              # 主页面文件
├── styles.css              # 样式文件
├── app.js                  # 主应用逻辑
├── config.js               # 配置文件
├── README.md               # 项目说明
├── memory-bank/            # 项目文档
│   ├── project-brief.md
│   ├── product-context.md
│   ├── architecture-design.md
│   ├── technical-specifications.md
│   ├── api-documentation.md
│   ├── user-stories.md
│   ├── testing-strategy.md
│   └── deployment-guide.md
└── assets/                 # 静态资源 (可选)
    ├── images/
    ├── icons/
    └── fonts/
```

### 文件命名规范
- **HTML文件**: 小写字母，连字符分隔 (kebab-case)
- **CSS文件**: 小写字母，连字符分隔 (kebab-case)
- **JavaScript文件**: 小写字母，连字符分隔 (kebab-case)
- **图片文件**: 小写字母，连字符分隔，包含尺寸信息
- **文档文件**: 小写字母，连字符分隔 (kebab-case)

## HTML规范

### 文档结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="OTA订单处理系统">
    <title>OTA订单处理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 页面内容 -->
    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
```

### 语义化标签使用
```html
<!-- 推荐的语义化结构 -->
<header>        <!-- 页面头部 -->
<nav>           <!-- 导航菜单 -->
<main>          <!-- 主要内容 -->
<section>       <!-- 内容区块 -->
<article>       <!-- 独立内容 -->
<aside>         <!-- 侧边内容 -->
<footer>        <!-- 页面底部 -->
```

### 可访问性规范
```html
<!-- ARIA标签使用 -->
<button aria-label="处理订单" aria-describedby="process-help">
<input aria-required="true" aria-invalid="false">
<div role="alert" aria-live="polite">

<!-- 表单标签关联 -->
<label for="customer-name">客户姓名</label>
<input id="customer-name" type="text">

<!-- 图片替代文本 -->
<img src="icon.png" alt="订单状态图标">
```

## CSS规范

### 代码组织结构
```css
/* 1. CSS Reset/Normalize */
/* 2. CSS变量定义 */
/* 3. 基础样式 */
/* 4. 布局样式 */
/* 5. 组件样式 */
/* 6. 工具类样式 */
/* 7. 响应式样式 */
/* 8. 动画样式 */
```

### CSS变量定义
```css
:root {
    /* 颜色系统 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    
    /* 间距系统 */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    
    /* 字体系统 */
    --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* 圆角系统 */
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    
    /* 过渡动画 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* 断点系统 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
}
```

### 命名规范 (BEM方法论)
```css
/* Block - 独立的组件 */
.card { }

/* Element - 组件的子元素 */
.card__header { }
.card__body { }
.card__footer { }

/* Modifier - 组件的变体 */
.card--large { }
.card--primary { }
.card__header--centered { }

/* 状态类 */
.is-active { }
.is-loading { }
.is-disabled { }
.is-hidden { }

/* 工具类 */
.text-center { }
.mb-4 { }
.flex { }
.grid { }
```

### 响应式设计规范
```css
/* Mobile First 方法 */
/* 基础样式 - 移动端 */
.container {
    padding: var(--spacing-md);
}

/* 平板端 */
@media (min-width: 768px) {
    .container {
        padding: var(--spacing-lg);
        max-width: 768px;
        margin: 0 auto;
    }
}

/* 桌面端 */
@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
        padding: var(--spacing-xl);
    }
}

/* 大屏幕 */
@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}
```

## JavaScript规范

### 代码组织结构
```javascript
// 1. 常量定义
// 2. 配置对象
// 3. 工具函数
// 4. 服务类
// 5. 主应用类
// 6. 初始化代码
```

### 命名规范
```javascript
// 常量 - 全大写，下划线分隔
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 变量和函数 - 驼峰命名
const userName = 'john';
const isLoggedIn = true;
function getUserData() { }
function processOrderData() { }

// 类名 - 帕斯卡命名
class OrderProcessor { }
class APIService { }
class GeminiService { }

// 私有方法/属性 - 下划线前缀
class MyClass {
    _privateMethod() { }
    _privateProperty = null;
}

// 事件处理函数 - handle前缀
function handleLoginClick() { }
function handleFormSubmit() { }

// DOM元素 - $前缀或Element后缀
const $loginButton = document.getElementById('login-btn');
const loginButtonElement = document.getElementById('login-btn');
```

### 函数定义规范
```javascript
// 函数文档注释
/**
 * 处理订单数据
 * @param {string} orderText - 订单文本内容
 * @param {string} otaType - OTA类型
 * @returns {Promise<Object>} 处理结果
 * @throws {Error} 当处理失败时抛出错误
 */
async function processOrderData(orderText, otaType) {
    // 参数验证
    if (!orderText || typeof orderText !== 'string') {
        throw new Error('订单文本不能为空');
    }
    
    if (!otaType || typeof otaType !== 'string') {
        throw new Error('OTA类型不能为空');
    }
    
    try {
        // 业务逻辑
        const result = await processData(orderText, otaType);
        return result;
    } catch (error) {
        console.error('处理订单数据失败:', error);
        throw error;
    }
}
```

### 错误处理规范
```javascript
// 自定义错误类
class APIError extends Error {
    constructor(message, statusCode, response) {
        super(message);
        this.name = 'APIError';
        this.statusCode = statusCode;
        this.response = response;
    }
}

class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
    }
}

// 错误处理函数
function handleError(error, context = '') {
    console.error(`[${context}] 错误:`, error);
    
    if (error instanceof APIError) {
        showErrorMessage(`API错误: ${error.message}`);
    } else if (error instanceof ValidationError) {
        showErrorMessage(`验证错误: ${error.message}`);
    } else {
        showErrorMessage('系统错误，请稍后重试');
    }
}

// 异步函数错误处理
async function safeAsyncOperation() {
    try {
        const result = await riskyOperation();
        return { success: true, data: result };
    } catch (error) {
        handleError(error, 'safeAsyncOperation');
        return { success: false, error: error.message };
    }
}
```

### 类定义规范
```javascript
/**
 * API服务类
 * 负责与后端API的通信
 */
class APIService {
    constructor(baseURL, timeout = 10000) {
        this.baseURL = baseURL;
        this.timeout = timeout;
        this.token = null;
    }
    
    /**
     * 设置认证令牌
     * @param {string} token - JWT令牌
     */
    setToken(token) {
        this.token = token;
    }
    
    /**
     * 发送HTTP请求
     * @param {string} endpoint - API端点
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应数据
     * @private
     */
    async _request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        
        if (this.token) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }
        
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new APIError(
                    `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    response
                );
            }
            
            return await response.json();
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            throw new APIError(`网络请求失败: ${error.message}`, 0, null);
        }
    }
    
    /**
     * 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async login(email, password) {
        const response = await this._request('/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });
        
        if (response.token) {
            this.setToken(response.token);
        }
        
        return response;
    }
}
```

### 模块化规范
```javascript
// 配置模块 (config.js)
const CONFIG = {
    API: {
        BASE_URL: 'https://staging.gomyhire.com.my/api',
        TIMEOUT: 10000,
        RETRY_COUNT: 3
    },
    GEMINI: {
        API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
        API_KEY: '', // 需要用户配置
        MODEL_CONFIG: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048
        }
    },
    STORAGE: {
        TOKEN_KEY: 'ota_system_token',
        USER_KEY: 'ota_system_user',
        CONFIG_KEY: 'ota_system_config'
    }
};

// 工具函数模块
const Utils = {
    /**
     * 格式化日期
     * @param {Date|string} date - 日期
     * @param {string} format - 格式
     * @returns {string} 格式化后的日期
     */
    formatDate(date, format = 'YYYY-MM-DD') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day);
    },
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
};
```

## API集成规范

### Gemini AI API集成
```javascript
/**
 * Gemini AI服务类
 */
class GeminiService {
    constructor(apiKey, config = {}) {
        this.apiKey = apiKey;
        this.config = {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
            ...config
        };
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models';
    }
    
    /**
     * 处理文本订单
     * @param {string} orderText - 订单文本
     * @param {string} prompt - 处理提示词
     * @returns {Promise<Object>} 处理结果
     */
    async processTextOrder(orderText, prompt) {
        const url = `${this.baseURL}/gemini-pro:generateContent?key=${this.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: `${prompt}\n\n订单内容：\n${orderText}`
                }]
            }],
            generationConfig: this.config
        };
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error(`Gemini API错误: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            return this._parseResponse(data);
        } catch (error) {
            console.error('Gemini API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理图片订单
     * @param {string} imageData - Base64图片数据
     * @param {string} prompt - 处理提示词
     * @returns {Promise<Object>} 处理结果
     */
    async processImageOrder(imageData, prompt) {
        const url = `${this.baseURL}/gemini-pro-vision:generateContent?key=${this.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [
                    {
                        text: `${prompt}\n\n请分析这张图片中的订单信息：`
                    },
                    {
                        inline_data: {
                            mime_type: "image/jpeg",
                            data: imageData
                        }
                    }
                ]
            }],
            generationConfig: this.config
        };
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error(`Gemini Vision API错误: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            return this._parseResponse(data);
        } catch (error) {
            console.error('Gemini Vision API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 解析API响应
     * @param {Object} response - API响应
     * @returns {Object} 解析后的结果
     * @private
     */
    _parseResponse(response) {
        try {
            if (!response.candidates || response.candidates.length === 0) {
                throw new Error('AI未返回有效结果');
            }
            
            const candidate = response.candidates[0];
            if (!candidate.content || !candidate.content.parts) {
                throw new Error('AI响应格式错误');
            }
            
            const text = candidate.content.parts[0].text;
            
            // 尝试解析JSON格式的响应
            try {
                const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[1]);
                }
                
                // 如果没有代码块，尝试直接解析
                return JSON.parse(text);
            } catch (parseError) {
                // 如果不是JSON格式，返回原始文本
                return {
                    success: false,
                    error: 'AI返回的不是有效的JSON格式',
                    raw_text: text
                };
            }
        } catch (error) {
            console.error('解析AI响应失败:', error);
            return {
                success: false,
                error: error.message,
                raw_response: response
            };
        }
    }
}
```

### GoMyHire API集成
```javascript
/**
 * GoMyHire API服务类
 */
class GoMyHireAPI extends APIService {
    constructor() {
        super('https://staging.gomyhire.com.my/api');
    }
    
    /**
     * 获取后台用户列表
     * @returns {Promise<Array>} 用户列表
     */
    async getBackendUsers() {
        return await this._request('/backend_users');
    }
    
    /**
     * 获取子分类列表
     * @returns {Promise<Array>} 子分类列表
     */
    async getSubCategories() {
        return await this._request('/sub_category');
    }
    
    /**
     * 获取车型列表
     * @returns {Promise<Array>} 车型列表
     */
    async getCarTypes() {
        return await this._request('/car_types');
    }
    
    /**
     * 创建订单
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object>} 创建结果
     */
    async createOrder(orderData) {
        // 验证必填字段
        const requiredFields = [
            'sub_category_id',
            'ota_reference_number',
            'car_type_id',
            'incharge_by_backend_user_id'
        ];
        
        for (const field of requiredFields) {
            if (!orderData[field]) {
                throw new ValidationError(`缺少必填字段: ${field}`, field);
            }
        }
        
        return await this._request('/create_order', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    }
}
```

## 数据处理规范

### 数据验证
```javascript
/**
 * 数据验证器
 */
class DataValidator {
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 验证电话号码
     * @param {string} phone - 电话号码
     * @returns {boolean} 是否有效
     */
    static isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{7,15}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }
    
    /**
     * 验证日期格式
     * @param {string} date - 日期字符串
     * @returns {boolean} 是否有效
     */
    static isValidDate(date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) return false;
        
        const d = new Date(date);
        return d instanceof Date && !isNaN(d);
    }
    
    /**
     * 验证时间格式
     * @param {string} time - 时间字符串
     * @returns {boolean} 是否有效
     */
    static isValidTime(time) {
        const timeRegex = /^([01]?\d|2[0-3]):[0-5]\d$/;
        return timeRegex.test(time);
    }
    
    /**
     * 验证订单数据
     * @param {Object} orderData - 订单数据
     * @returns {Object} 验证结果
     */
    static validateOrderData(orderData) {
        const errors = [];
        const warnings = [];
        
        // 必填字段验证
        if (!orderData.customer_name) {
            errors.push('客户姓名不能为空');
        }
        
        if (!orderData.customer_contact) {
            errors.push('客户联系方式不能为空');
        } else if (!this.isValidPhone(orderData.customer_contact) && !this.isValidEmail(orderData.customer_contact)) {
            warnings.push('客户联系方式格式可能不正确');
        }
        
        if (!orderData.date) {
            errors.push('日期不能为空');
        } else if (!this.isValidDate(orderData.date)) {
            errors.push('日期格式不正确，应为YYYY-MM-DD');
        }
        
        if (!orderData.time) {
            errors.push('时间不能为空');
        } else if (!this.isValidTime(orderData.time)) {
            errors.push('时间格式不正确，应为HH:MM');
        }
        
        if (!orderData.pickup) {
            errors.push('上车地点不能为空');
        }
        
        if (!orderData.destination) {
            errors.push('目的地不能为空');
        }
        
        // 数值验证
        if (orderData.passenger_number && (orderData.passenger_number < 1 || orderData.passenger_number > 50)) {
            warnings.push('乘客数量应在1-50之间');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
```

### 数据转换
```javascript
/**
 * 数据转换器
 */
class DataTransformer {
    /**
     * 转换AI处理结果为订单数据
     * @param {Object} aiResult - AI处理结果
     * @returns {Object} 订单数据
     */
    static transformAIResultToOrder(aiResult) {
        const orderData = {
            // 基础信息
            customer_name: aiResult.customer_name || '',
            customer_contact: aiResult.customer_contact || aiResult.phone || '',
            passenger_number: parseInt(aiResult.passenger_number) || 1,
            
            // 时间地点
            date: aiResult.date || '',
            time: aiResult.time || '',
            pickup: aiResult.pickup || aiResult.pickup_location || '',
            destination: aiResult.destination || aiResult.dropoff_location || '',
            
            // 航班信息
            flight_info: aiResult.flight_info || '',
            flight_number: aiResult.flight_number || '',
            flight_time: aiResult.flight_time || '',
            
            // OTA信息
            ota_reference_number: aiResult.ota_reference_number || aiResult.reference || '',
            
            // 备注
            notes: aiResult.notes || aiResult.remarks || ''
        };
        
        // 清理空值
        Object.keys(orderData).forEach(key => {
            if (orderData[key] === '' || orderData[key] === null || orderData[key] === undefined) {
                delete orderData[key];
            }
        });
        
        return orderData;
    }
    
    /**
     * 转换订单数据为API格式
     * @param {Object} orderData - 订单数据
     * @param {Object} systemData - 系统数据
     * @returns {Object} API格式的订单数据
     */
    static transformOrderToAPI(orderData, systemData) {
        return {
            // 必填字段
            sub_category_id: systemData.sub_category_id,
            ota_reference_number: orderData.ota_reference_number,
            car_type_id: systemData.car_type_id,
            incharge_by_backend_user_id: systemData.incharge_by_backend_user_id,
            
            // 可选字段
            customer_name: orderData.customer_name,
            customer_contact: orderData.customer_contact,
            flight_info: orderData.flight_info,
            pickup: orderData.pickup,
            destination: orderData.destination,
            date: orderData.date,
            time: orderData.time,
            passenger_number: orderData.passenger_number,
            notes: orderData.notes
        };
    }
}
```

## 存储规范

### LocalStorage管理
```javascript
/**
 * 本地存储服务
 */
class StorageService {
    constructor(prefix = 'ota_system_') {
        this.prefix = prefix;
    }
    
    /**
     * 生成存储键名
     * @param {string} key - 键名
     * @returns {string} 完整键名
     * @private
     */
    _getKey(key) {
        return `${this.prefix}${key}`;
    }
    
    /**
     * 保存数据
     * @param {string} key - 键名
     * @param {*} value - 值
     * @param {number} expiry - 过期时间(毫秒)
     */
    set(key, value, expiry = null) {
        try {
            const data = {
                value,
                timestamp: Date.now(),
                expiry: expiry ? Date.now() + expiry : null
            };
            
            localStorage.setItem(this._getKey(key), JSON.stringify(data));
        } catch (error) {
            console.error('保存数据失败:', error);
            throw new Error('存储空间不足或数据无法序列化');
        }
    }
    
    /**
     * 获取数据
     * @param {string} key - 键名
     * @param {*} defaultValue - 默认值
     * @returns {*} 存储的值
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this._getKey(key));
            if (!item) return defaultValue;
            
            const data = JSON.parse(item);
            
            // 检查是否过期
            if (data.expiry && Date.now() > data.expiry) {
                this.remove(key);
                return defaultValue;
            }
            
            return data.value;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }
    
    /**
     * 删除数据
     * @param {string} key - 键名
     */
    remove(key) {
        localStorage.removeItem(this._getKey(key));
    }
    
    /**
     * 清空所有数据
     */
    clear() {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith(this.prefix)) {
                localStorage.removeItem(key);
            }
        });
    }
    
    /**
     * 获取存储使用情况
     * @returns {Object} 存储统计
     */
    getStorageInfo() {
        let totalSize = 0;
        let itemCount = 0;
        
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith(this.prefix)) {
                totalSize += localStorage.getItem(key).length;
                itemCount++;
            }
        });
        
        return {
            itemCount,
            totalSize,
            totalSizeKB: Math.round(totalSize / 1024 * 100) / 100
        };
    }
}
```

## 性能优化规范

### 图片处理优化
```javascript
/**
 * 图片处理工具
 */
class ImageProcessor {
    /**
     * 压缩图片
     * @param {File} file - 图片文件
     * @param {Object} options - 压缩选项
     * @returns {Promise<string>} Base64数据
     */
    static async compressImage(file, options = {}) {
        const {
            maxWidth = 1920,
            maxHeight = 1080,
            quality = 0.8,
            format = 'image/jpeg'
        } = options;
        
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // 计算新尺寸
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
                
                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }
                
                // 设置画布尺寸
                canvas.width = width;
                canvas.height = height;
                
                // 绘制图片
                ctx.drawImage(img, 0, 0, width, height);
                
                // 转换为Base64
                const base64 = canvas.toDataURL(format, quality);
                resolve(base64.split(',')[1]); // 移除data:image/jpeg;base64,前缀
            };
            
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    }
    
    /**
     * 验证图片文件
     * @param {File} file - 图片文件
     * @returns {Object} 验证结果
     */
    static validateImageFile(file) {
        const errors = [];
        const warnings = [];
        
        // 文件类型检查
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            errors.push('不支持的图片格式，请使用JPG、PNG、GIF或WebP格式');
        }
        
        // 文件大小检查
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            errors.push('图片文件过大，请选择小于10MB的图片');
        } else if (file.size > 5 * 1024 * 1024) {
            warnings.push('图片文件较大，处理可能需要更长时间');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
```

### 网络请求优化
```javascript
/**
 * 网络请求工具
 */
class NetworkUtils {
    /**
     * 带重试的fetch请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @param {number} retries - 重试次数
     * @returns {Promise<Response>} 响应对象
     */
    static async fetchWithRetry(url, options = {}, retries = 3) {
        for (let i = 0; i <= retries; i++) {
            try {
                const response = await fetch(url, {
                    timeout: 10000,
                    ...options
                });
                
                if (response.ok) {
                    return response;
                }
                
                // 如果是客户端错误(4xx)，不重试
                if (response.status >= 400 && response.status < 500) {
                    throw new Error(`客户端错误: ${response.status} ${response.statusText}`);
                }
                
                // 服务器错误(5xx)，继续重试
                if (i === retries) {
                    throw new Error(`服务器错误: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                if (i === retries) {
                    throw error;
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
            }
        }
    }
    
    /**
     * 并发请求控制
     * @param {Array} requests - 请求数组
     * @param {number} concurrency - 并发数
     * @returns {Promise<Array>} 结果数组
     */
    static async batchRequests(requests, concurrency = 3) {
        const results = [];
        
        for (let i = 0; i < requests.length; i += concurrency) {
            const batch = requests.slice(i, i + concurrency);
            const batchResults = await Promise.allSettled(batch);
            results.push(...batchResults);
        }
        
        return results;
    }
}
```

## 测试规范

### 单元测试
```javascript
/**
 * 测试工具函数
 */
class TestUtils {
    /**
     * 简单的断言函数
     * @param {boolean} condition - 条件
     * @param {string} message - 错误信息
     */
    static assert(condition, message) {
        if (!condition) {
            throw new Error(`断言失败: ${message}`);
        }
    }
    
    /**
     * 测试异步函数
     * @param {Function} asyncFn - 异步函数
     * @param {*} expectedResult - 期望结果
     * @param {string} testName - 测试名称
     */
    static async testAsync(asyncFn, expectedResult, testName) {
        try {
            const result = await asyncFn();
            this.assert(
                JSON.stringify(result) === JSON.stringify(expectedResult),
                `${testName}: 期望 ${JSON.stringify(expectedResult)}, 实际 ${JSON.stringify(result)}`
            );
            console.log(`✓ ${testName} 通过`);
        } catch (error) {
            console.error(`✗ ${testName} 失败:`, error.message);
        }
    }
    
    /**
     * 运行测试套件
     * @param {Object} tests - 测试对象
     */
    static async runTests(tests) {
        console.log('开始运行测试...');
        
        let passed = 0;
        let failed = 0;
        
        for (const [testName, testFn] of Object.entries(tests)) {
            try {
                await testFn();
                passed++;
            } catch (error) {
                console.error(`测试失败 [${testName}]:`, error.message);
                failed++;
            }
        }
        
        console.log(`\n测试完成: ${passed} 通过, ${failed} 失败`);
    }
}

// 示例测试
const tests = {
    async testDataValidator() {
        TestUtils.assert(
            DataValidator.isValidEmail('<EMAIL>'),
            '有效邮箱应该通过验证'
        );
        
        TestUtils.assert(
            !DataValidator.isValidEmail('invalid-email'),
            '无效邮箱应该验证失败'
        );
    },
    
    async testStorageService() {
        const storage = new StorageService('test_');
        
        storage.set('key1', 'value1');
        TestUtils.assert(
            storage.get('key1') === 'value1',
            '存储和读取应该一致'
        );
        
        storage.remove('key1');
        TestUtils.assert(
            storage.get('key1') === null,
            '删除后应该返回null'
        );
    }
};

// 运行测试
// TestUtils.runTests(tests);
```

## 部署规范

### 构建脚本
```javascript
/**
 * 简单的构建工具
 */
class BuildTool {
    /**
     * 压缩CSS
     * @param {string} css - CSS内容
     * @returns {string} 压缩后的CSS
     */
    static minifyCSS(css) {
        return css
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
            .replace(/\s+/g, ' ') // 压缩空白
            .replace(/;\s*}/g, '}') // 移除最后的分号
            .replace(/\s*{\s*/g, '{') // 压缩大括号
            .replace(/;\s*/g, ';') // 压缩分号
            .trim();
    }
    
    /**
     * 压缩JavaScript
     * @param {string} js - JavaScript内容
     * @returns {string} 压缩后的JavaScript
     */
    static minifyJS(js) {
        return js
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
            .replace(/\/\/.*$/gm, '') // 移除行注释
            .replace(/\s+/g, ' ') // 压缩空白
            .replace(/;\s*}/g, '}') // 移除最后的分号
            .trim();
    }
    
    /**
     * 生成版本信息
     * @returns {Object} 版本信息
     */
    static generateVersionInfo() {
        return {
            version: '1.0.0',
            buildTime: new Date().toISOString(),
            buildNumber: Date.now(),
            gitCommit: 'unknown' // 在实际项目中可以从git获取
        };
    }
}
```

### 环境配置
```javascript
/**
 * 环境配置管理
 */
class EnvironmentConfig {
    static getConfig(env = 'production') {
        const configs = {
            development: {
                API_BASE_URL: 'https://staging.gomyhire.com.my/api',
                GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models',
                DEBUG: true,
                LOG_LEVEL: 'debug'
            },
            staging: {
                API_BASE_URL: 'https://staging.gomyhire.com.my/api',
                GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models',
                DEBUG: false,
                LOG_LEVEL: 'info'
            },
            production: {
                API_BASE_URL: 'https://api.gomyhire.com.my/api',
                GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models',
                DEBUG: false,
                LOG_LEVEL: 'error'
            }
        };
        
        return configs[env] || configs.production;
    }
}
```

这份技术规范文档详细定义了OTA订单处理系统的技术实现标准，包括代码规范、API集成、数据处理、存储管理、性能优化、测试和部署等各个方面，为项目开发提供了完整的技术指导。