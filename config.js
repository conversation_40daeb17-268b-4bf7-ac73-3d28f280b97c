/**
 * @file config.js - 系统配置文件
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 系统配置
const SYSTEM_CONFIG = {
    // API配置
    API: {
        // GoMyHire API基础URL
        BASE_URL: 'https://staging.gomyhire.com.my/api',
        
        // Gemini AI API配置
        GEMINI: {
            // 请替换为您的实际Gemini API Key
            // 获取方式：https://makersuite.google.com/app/apikey
            API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
            API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
            
            // 模型配置
            MODEL_CONFIG: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 2048
            }
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        TOKEN: 'ota_system_token',
        USER_INFO: 'ota_system_user',
        BACKEND_USERS: 'ota_backend_users',
        SUB_CATEGORIES: 'ota_sub_categories',
        CAR_TYPES: 'ota_car_types',
        LAST_LOGIN: 'ota_last_login'
    },
    
    // 默认登录信息
    DEFAULT_LOGIN: {
        email: '<EMAIL>',
        password: '1234'
    },
    
    // OTA处理配置
    OTA_TYPES: {
        'chong-dealer': {
            name: 'Chong Dealer',
            description: '重庆经销商订单处理',
            promptFile: 'prompts/chong-dealer.txt'
        },
        'auto': {
            name: '自动识别',
            description: '根据订单内容自动识别OTA类型'
        },
        'other': {
            name: '其他',
            description: '通用订单处理'
        }
    },
    
    // 文件上传配置
    UPLOAD: {
        MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        MAX_FILES: 10
    },
    
    // 系统设置
    SYSTEM: {
        AUTO_SAVE_INTERVAL: 30000, // 30秒自动保存
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000
    }
};

// 导出配置（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SYSTEM_CONFIG;
}

// 全局配置（浏览器环境）
if (typeof window !== 'undefined') {
    window.SYSTEM_CONFIG = SYSTEM_CONFIG;
}