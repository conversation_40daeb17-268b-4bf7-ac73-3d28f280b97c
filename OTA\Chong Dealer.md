请根据以下需求，对“【订单列表】”中的每条订单信息进行处理，并输出为**纯文本**格式，每条订单之间用空行分隔：

【Chong Dealer 默认预设配置】

⚠️ **重要说明**：以下字段已有默认预设值，Gemini AI 只需要返回除这些字段以外的内容：

**默认预设字段：**
- `ota` = "Chong Dealer"
- `ota_reference_number` = 随机生成（基于「订单日期 + 时间 + 航班号 + 客人名字」进行随机或组合式生成，需保持唯一性）
- `ota_price` = 0
- `customer_contact` = 随机生成
- `customer_email` = "<EMAIL>"
- `passenger_number` = 1
- `luggage_number` = 1
- `driver_fee` = 1
- `extra_requirement` = "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"

------------------------------------------------
【需求说明】

**总体处理流程指引：**
1.  **优先提取原始信息**：仔细阅读每个订单的原始文本，准确提取所有明确给出的基本信息（如原始日期、时间、航班号、客人姓名、酒店名称等）。
2.  **日期修正与推断**：根据下述“多重日期判断与修正”规则，结合当前日期，计算并确定最终的服务日期。
3.  **时间计算**：根据服务类型（接机/送机）和提取到的航班时间，计算准确的订单时间。
4.  **地点标准化**：转换酒店名称为标准英文，并根据服务类型确定上下车地点。
5.  **信息汇总与输出**：将处理后的信息整理成指定的纯文本格式进行输出，并将无法处理或不确定的信息记录在 `other` 字段。

---


1. **多重日期判断与修正：**  
   - **核心原则**：以当前日期为锚点，对订单中填写的原始日期进行严谨的验算与推断，确保**最终输出的服务日期**是**距离当前最近且逻辑合理的未来日期**。
   - **处理逻辑**：
     a. **有效性检查**：首先判断原始订单日期是否已过时或相对于当前日期不再合理。
     b. **顺延机制**：若日期已过或不合理，则应基于原始日期进行顺延，目标是找到一个**最接近原始意图日期**但必须位于未来的有效日期。
     c. **当日/未来日期**：若订单日期恰好是今天，或尚未到达，并且从业务逻辑上看是可行的（例如，送机时间不会早于当前时间太多），则可采纳原始日期或经过最小合理调整的日期。
   - **优先级验证与推算（请依次尝试）：**  
     i. **同月优先**：如果原始日期的“日”在本月尚未过去且逻辑上可用（例如，今天15号，订单是本月20号），则使用本月的该日期。
     ii. **下月同日**：如果本月的该“日”已过或不合理，则尝试顺延到下个月的同一“日”。
     iii. **特殊情况处理（跨年/无效日期）**：如果遇到跨年份（如12月顺延到1月）或当月无对应天数（如原始订单为 “2.30”，但2月没有30号），则必须顺延至下一个逻辑上最接近且可行的日期（例如，“2.30”在平年应顺延至“3.2”或“3.1”；“12.28”若今年已过，则顺延至明年的“1.28”）。
   - **示例**：假设今天是2024年5月15日。
     - 订单日期 “5.10” -> 已过，若客人意图是最近的10号，则应推断为 “2024-06-10”。
     - 订单日期 “1.20” -> 已过，若客人意图是最近的1.20，则应推断为 “2025-01-20”。
     - 订单日期 “2.29”（在2025年，非闰年）-> 无效，应推断为 “2025-03-01”。
     - 订单日期 “12.30” (今天是2024-12-28) -> 未到，可用 “2024-12-30”。
     - 订单日期 “12.30” (今天是2024-12-31) -> 已过，应推断为 “2025-01-30” (如果客人意图是下个月的30号) 或 “2025-12-30” (如果客人意图是明年的这个日期，需要结合上下文判断，若无更多信息，优先选择最近的未来日期)。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 直接使用订单中提供的航班"到达时间" (Arrival Time) 作为 `订单时间` (HH:MM格式)。  
   - **送机 (dropoff)**：  
     - 获取订单中提供的航班"起飞时间" (Departure Time)，在此基础上**向前推算 3.5 小时** (即减去3小时30分钟)，将计算结果作为 `订单时间` (HH:MM格式)。  
   - **时间格式处理与异常**：  
     - 努力从原始文本中解析时间，即使格式不完全标准（例如 “22.20”、“晚上10点20”）。
     - 若原始时间信息缺失、完全无法解析或计算后不合逻辑（如送机时间晚于起飞时间），请在 `other` 字段中清晰注明原始时间信息和遇到的问题，并尝试为 `订单时间` 赋予一个基于上下文推断的合理默认值（例如，接机默认为中午12:00，送机默认为早上08:00），同时在 `other` 字段中说明此为推断值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **上下车地点逻辑：**  
   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。

5. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - **Gemini 只返回以下字段**（不返回默认预设字段）：
     - 日期: YYYY-MM-DD
     - 时间: HH:MM
     - 姓名: [客人姓名]
     - 航班: [航班号]
     - pickup: [上车地点]
     - drop: [下车地点]
     - other: [其他信息]

------------------------------------------------
【订单列表】
在此处粘贴或输入订单信息，每个订单之间可使用空行分隔。请确保准确提取以下关键信息用于后续处理：
- **原始日期文本** (例如: "1.28", "明天", "下周一")
- **服务类型** (例如: "接机", "送机", "pickup", "dropoff")
- **航班号** (例如: "KE671", "AK378")
- **航班时间文本** (例如: "22.20抵达", "16.20起飞", "晚上10点到")
- **客人姓名** (例如: "张梦媛")
- **酒店名称文本** (例如: "Santa Grand Signature", "圣淘沙")

**示例订单：**
1.28接机：KE671 22.20抵达 
1.30送机：AK378 16.20起飞

联系人：张梦媛 
人数：6
车型：商务十座
酒店：Santa Grand Signature 

JY

假设当前日期为2025年1月27日，处理结果应为：

日期: 2025-01-28
时间: 22:20
姓名: 张梦媛
航班: KE671
pickup: klia
drop: Santa Grand Signature Hotel Kuala Lumpur (此为示例，实际需查询)
other: 原始人数:6, 车型:商务十座, 原始联系人:JY

日期: 2025-01-30
时间: 12:50
姓名: 张梦媛
航班: AK378
pickup: Santa Grand Signature Hotel Kuala Lumpur (此为示例，实际需查询)
drop: klia
other: 原始人数:6, 车型:商务十座, 原始联系人:JY, 原始起飞时间16:20

（不显示网络搜索结果）