/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
}

.success-message {
    color: #27ae60;
    margin-top: 10px;
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

/* 登录表单 */
#loginForm {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

button:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 15px 30px;
    font-size: 18px;
    font-weight: 600;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 主应用界面 */
#mainApp {
    min-height: 100vh;
    background: #f8f9fa;
}

header {
    background: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    color: #667eea;
    font-size: 28px;
    font-weight: 700;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

#userInfo {
    color: #666;
    font-weight: 500;
}

#logoutBtn {
    background: #e74c3c;
    padding: 8px 16px;
    font-size: 14px;
}

#logoutBtn:hover {
    background: #c0392b;
}

/* 主内容区域 */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.section {
    background: white;
    margin-bottom: 30px;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

/* 标签页 */
.input-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    font-weight: 600;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 文字输入区域 */
#orderText {
    width: 100%;
    min-height: 200px;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    resize: vertical;
    transition: border-color 0.3s;
}

#orderText:focus {
    outline: none;
    border-color: #667eea;
}

/* 图片上传区域 */
.upload-area {
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e8f0ff;
}

#imageFile {
    display: none;
}

.image-preview {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.image-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

/* OTA选择 */
.ota-selection {
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ota-selection label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #555;
}

#otaSelect {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background: white;
}

/* 结果预览 */
.result-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
}

.result-content {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #ddd;
    min-height: 200px;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    line-height: 1.6;
}

.order-item {
    background: white;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-item h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 18px;
}

.order-field {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.order-field label {
    font-weight: 600;
    color: #555;
    width: 120px;
    flex-shrink: 0;
}

.order-field input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 10px;
}

/* 操作按钮 */
.action-buttons {
    margin-top: 25px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

#exportBtn {
    background: #27ae60;
}

#exportBtn:hover {
    background: #229954;
}

/* 状态显示 */
#statusContent {
    padding: 20px;
}

.status-item {
    background: #f8f9fa;
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.status-item.error {
    border-left-color: #e74c3c;
    background: #fdf2f2;
}

.status-item.pending {
    border-left-color: #f39c12;
    background: #fef9e7;
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .user-info {
        justify-content: center;
    }
    
    main {
        padding: 20px 15px;
    }
    
    .section {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .result-controls {
        flex-direction: column;
    }
    
    .order-field {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .order-field label {
        width: auto;
        margin-bottom: 5px;
    }
    
    .order-field input {
        margin-left: 0;
        width: 100%;
    }
}