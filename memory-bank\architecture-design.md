# 系统架构设计 - OTA订单处理系统

## 架构概述

### 架构原则
1. **纯前端架构**: 所有逻辑在浏览器中执行，无需后端服务器
2. **模块化设计**: 功能模块独立，低耦合高内聚
3. **API优先**: 通过标准API与外部服务集成
4. **响应式设计**: 适配不同设备和屏幕尺寸
5. **渐进增强**: 基础功能优先，高级功能可选

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    浏览器环境 (Browser)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   用户界面层     │  │   业务逻辑层     │  │   数据访问层     │ │
│  │ (Presentation)  │  │   (Business)    │  │    (Data)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      外部服务层                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Gemini AI     │  │  GoMyHire API   │  │  LocalStorage   │ │
│  │     服务        │  │      服务       │  │      存储       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 分层架构设计

### 1. 用户界面层 (Presentation Layer)

#### 1.1 组件结构
```
UI Components
├── App (主应用容器)
│   ├── LoginModal (登录模态框)
│   ├── Header (页面头部)
│   ├── MainContent (主内容区)
│   │   ├── InputTabs (输入标签页)
│   │   │   ├── TextInput (文字输入)
│   │   │   └── ImageUpload (图片上传)
│   │   ├── OTASelector (OTA类型选择)
│   │   ├── ProcessButton (处理按钮)
│   │   ├── ResultPreview (结果预览)
│   │   └── ActionButtons (操作按钮)
│   └── StatusBar (状态栏)
└── Utils
    ├── LoadingSpinner (加载动画)
    ├── ErrorMessage (错误提示)
    └── SuccessMessage (成功提示)
```

#### 1.2 界面状态管理
```javascript
// 应用状态枚举
const APP_STATES = {
    LOADING: 'loading',           // 加载中
    LOGIN_REQUIRED: 'login',      // 需要登录
    READY: 'ready',              // 就绪状态
    PROCESSING: 'processing',     // 处理中
    PREVIEW: 'preview',          // 预览结果
    CREATING: 'creating',        // 创建订单中
    COMPLETED: 'completed'       // 完成
};

// 界面状态转换
State Transitions:
LOADING → LOGIN_REQUIRED → READY → PROCESSING → PREVIEW → CREATING → COMPLETED
                    ↑         ↑                    ↓
                    └─────────┴────────────────────┘
```

#### 1.3 响应式设计
- **桌面端**: 1200px+ 三栏布局
- **平板端**: 768px-1199px 两栏布局
- **手机端**: <768px 单栏布局
- **断点设置**: 使用CSS媒体查询实现

### 2. 业务逻辑层 (Business Logic Layer)

#### 2.1 核心服务模块

```javascript
// 服务架构
Services
├── AuthService (认证服务)
│   ├── login()
│   ├── logout()
│   ├── checkAuthStatus()
│   └── refreshToken()
├── APIService (API服务)
│   ├── getBackendUsers()
│   ├── getSubCategories()
│   ├── getCarTypes()
│   └── createOrder()
├── GeminiService (AI服务)
│   ├── processTextOrder()
│   ├── processImageOrder()
│   └── generatePrompt()
├── OrderProcessor (订单处理器)
│   ├── parseOrderData()
│   ├── validateOrderData()
│   ├── formatOrderData()
│   └── generateReference()
└── StorageService (存储服务)
    ├── saveToLocal()
    ├── loadFromLocal()
    ├── clearStorage()
    └── exportData()
```

#### 2.2 数据流设计

```
用户输入 → 数据验证 → AI处理 → 结果解析 → 用户确认 → API调用 → 状态更新
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  Input   Validate  Process   Parse    Confirm   Create   Update
   Data     Data      AI      Result   Result   Order    Status
```

#### 2.3 错误处理机制

```javascript
// 错误类型定义
const ERROR_TYPES = {
    NETWORK_ERROR: 'network',      // 网络错误
    API_ERROR: 'api',             // API错误
    VALIDATION_ERROR: 'validation', // 验证错误
    AI_ERROR: 'ai',               // AI处理错误
    STORAGE_ERROR: 'storage'       // 存储错误
};

// 错误处理策略
Error Handling Strategy:
1. 捕获所有异常
2. 分类错误类型
3. 提供用户友好的错误信息
4. 实现自动重试机制
5. 记录错误日志
6. 提供恢复建议
```

### 3. 数据访问层 (Data Access Layer)

#### 3.1 本地存储设计

```javascript
// LocalStorage 数据结构
LocalStorage Schema:
{
    // 认证信息
    "ota_system_token": "jwt_token_string",
    "ota_system_user": {
        "id": 1,
        "email": "<EMAIL>",
        "name": "User Name"
    },
    
    // 系统数据缓存
    "ota_backend_users": [...],
    "ota_sub_categories": [...],
    "ota_car_types": [...],
    
    // 会话数据
    "ota_last_login": "2024-12-19T10:00:00Z",
    "ota_current_session": {
        "input_data": "...",
        "processed_results": [...],
        "current_step": "preview"
    },
    
    // 配置信息
    "ota_config": {
        "gemini_api_key": "encrypted_key",
        "default_ota_type": "auto",
        "auto_save_enabled": true
    }
}
```

#### 3.2 数据模型定义

```javascript
// 订单数据模型
const OrderModel = {
    // 基础信息
    id: String,                    // 订单ID
    ota_reference_number: String,  // OTA参考号
    customer_name: String,         // 客户姓名
    customer_contact: String,      // 客户联系方式
    passenger_number: Number,      // 乘客数量
    
    // 服务信息
    service_type: String,          // 服务类型 (pickup/dropoff)
    sub_category_id: Number,       // 子分类ID
    car_type_id: Number,          // 车型ID
    
    // 时间地点
    date: String,                 // 日期 (YYYY-MM-DD)
    time: String,                 // 时间 (HH:MM)
    pickup: String,               // 上车地点
    destination: String,          // 目的地
    
    // 航班信息
    flight_info: String,          // 航班信息
    flight_number: String,        // 航班号
    flight_time: String,          // 航班时间
    
    // 处理信息
    processed_at: String,         // 处理时间
    processed_by: String,         // 处理人
    status: String,               // 状态
    notes: String                 // 备注
};

// AI处理结果模型
const AIResultModel = {
    success: Boolean,             // 处理是否成功
    confidence: Number,           // 置信度 (0-1)
    extracted_data: OrderModel,   // 提取的数据
    raw_response: String,         // AI原始响应
    processing_time: Number,      // 处理时间(ms)
    errors: Array,               // 错误信息
    warnings: Array              // 警告信息
};
```

## 外部服务集成

### 1. Gemini AI 服务集成

#### 1.1 API配置
```javascript
const GEMINI_CONFIG = {
    API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    VISION_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent',
    API_KEY: 'YOUR_API_KEY',
    
    // 模型参数
    MODEL_CONFIG: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048
    },
    
    // 重试配置
    RETRY_CONFIG: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2
    }
};
```

#### 1.2 请求格式
```javascript
// 文字处理请求
const textRequest = {
    contents: [{
        parts: [{
            text: `${PROMPT}\n\n订单内容：\n${orderText}`
        }]
    }],
    generationConfig: GEMINI_CONFIG.MODEL_CONFIG
};

// 图片处理请求
const imageRequest = {
    contents: [{
        parts: [
            {
                text: `${PROMPT}\n\n请分析这张图片中的订单信息：`
            },
            {
                inline_data: {
                    mime_type: "image/jpeg",
                    data: base64ImageData
                }
            }
        ]
    }],
    generationConfig: GEMINI_CONFIG.MODEL_CONFIG
};
```

#### 1.3 响应处理
```javascript
// 响应解析流程
Response Processing:
1. 检查HTTP状态码
2. 解析JSON响应
3. 提取生成的文本
4. 解析结构化数据
5. 验证数据完整性
6. 处理错误和警告
```

### 2. GoMyHire API 集成

#### 2.1 API端点
```javascript
const GOMYHIRE_API = {
    BASE_URL: 'https://staging.gomyhire.com.my/api',
    
    ENDPOINTS: {
        LOGIN: '/login',
        BACKEND_USERS: '/backend_users',
        SUB_CATEGORIES: '/sub_category',
        CAR_TYPES: '/car_types',
        CREATE_ORDER: '/create_order'
    }
};
```

#### 2.2 认证机制
```javascript
// 登录流程
Authentication Flow:
1. 发送登录请求 (email + password)
2. 接收JWT token
3. 存储token到LocalStorage
4. 在后续请求中添加Authorization header
5. 处理token过期和刷新
```

#### 2.3 数据映射
```javascript
// 订单数据映射
const mapOrderToAPI = (orderData) => {
    return {
        // 必填字段
        sub_category_id: orderData.sub_category_id,
        ota_reference_number: orderData.ota_reference_number,
        car_type_id: orderData.car_type_id,
        incharge_by_backend_user_id: orderData.incharge_by_backend_user_id,
        
        // 可选字段
        customer_name: orderData.customer_name,
        customer_contact: orderData.customer_contact,
        flight_info: orderData.flight_info,
        pickup: orderData.pickup,
        destination: orderData.destination,
        date: orderData.date,
        time: orderData.time,
        passenger_number: orderData.passenger_number,
        notes: orderData.notes
    };
};
```

## 安全架构

### 1. 数据安全

#### 1.1 敏感信息处理
```javascript
// 敏感数据处理策略
Sensitive Data Handling:
1. API密钥加密存储
2. 用户密码不在前端存储
3. 订单数据临时存储，定期清理
4. 个人信息脱敏处理
5. 传输过程HTTPS加密
```

#### 1.2 存储安全
```javascript
// LocalStorage安全策略
Storage Security:
1. 敏感数据加密存储
2. 设置数据过期时间
3. 页面关闭时清理临时数据
4. 防止XSS攻击
5. 定期清理过期数据
```

### 2. API安全

#### 2.1 请求安全
```javascript
// API请求安全措施
API Security:
1. HTTPS强制传输
2. JWT token认证
3. 请求频率限制
4. 输入数据验证
5. 错误信息脱敏
```

#### 2.2 错误处理安全
```javascript
// 安全的错误处理
Secure Error Handling:
1. 不暴露系统内部信息
2. 统一错误响应格式
3. 记录安全相关错误
4. 防止信息泄露
5. 用户友好的错误提示
```

## 性能优化

### 1. 前端性能优化

#### 1.1 资源优化
```javascript
// 资源加载优化
Resource Optimization:
1. CSS/JS文件压缩
2. 图片懒加载
3. 关键资源预加载
4. 非关键资源延迟加载
5. 浏览器缓存利用
```

#### 1.2 渲染优化
```javascript
// 渲染性能优化
Rendering Optimization:
1. 虚拟滚动（大量数据）
2. 防抖和节流
3. DOM操作批量处理
4. CSS动画硬件加速
5. 避免重排和重绘
```

### 2. 网络性能优化

#### 2.1 请求优化
```javascript
// 网络请求优化
Network Optimization:
1. 请求合并
2. 数据缓存
3. 并发请求控制
4. 超时处理
5. 重试机制
```

#### 2.2 数据传输优化
```javascript
// 数据传输优化
Data Transfer Optimization:
1. JSON数据压缩
2. 图片格式优化
3. 分页加载
4. 增量更新
5. CDN加速
```

## 可扩展性设计

### 1. 模块化架构

#### 1.1 插件系统
```javascript
// 插件架构设计
Plugin Architecture:
1. 核心系统与插件分离
2. 标准化插件接口
3. 动态插件加载
4. 插件生命周期管理
5. 插件间通信机制
```

#### 1.2 配置系统
```javascript
// 配置管理系统
Configuration System:
1. 分层配置管理
2. 运行时配置更新
3. 配置验证机制
4. 默认配置回退
5. 配置版本管理
```

### 2. 功能扩展

#### 2.1 OTA类型扩展
```javascript
// OTA处理器扩展
OTA Processor Extension:
1. 标准化处理接口
2. 动态处理器注册
3. 处理规则配置化
4. 自定义提示词支持
5. 处理结果标准化
```

#### 2.2 AI服务扩展
```javascript
// AI服务扩展
AI Service Extension:
1. 多AI服务支持
2. 服务切换机制
3. 结果融合策略
4. 性能监控
5. 成本控制
```

## 监控与调试

### 1. 性能监控

#### 1.1 关键指标
```javascript
// 性能监控指标
Performance Metrics:
1. 页面加载时间
2. API响应时间
3. AI处理时间
4. 内存使用情况
5. 错误率统计
```

#### 1.2 用户行为监控
```javascript
// 用户行为分析
User Behavior Analytics:
1. 功能使用频率
2. 用户操作路径
3. 错误发生模式
4. 性能瓶颈识别
5. 用户满意度指标
```

### 2. 调试支持

#### 2.1 开发工具
```javascript
// 开发调试工具
Development Tools:
1. 详细日志记录
2. 状态检查器
3. API请求监控
4. 性能分析器
5. 错误追踪
```

#### 2.2 生产环境调试
```javascript
// 生产环境调试
Production Debugging:
1. 远程日志收集
2. 错误自动报告
3. 性能数据收集
4. 用户反馈收集
5. 问题重现工具
```

## 部署架构

### 1. 静态部署

#### 1.1 文件结构
```
Deployment Structure:
├── index.html (入口文件)
├── styles.css (样式文件)
├── app.js (主应用)
├── config.js (配置文件)
├── assets/ (静态资源)
│   ├── images/
│   ├── fonts/
│   └── icons/
├── docs/ (文档)
│   ├── README.md
│   └── user-guide.md
└── memory-bank/ (项目文档)
    ├── project-brief.md
    ├── architecture-design.md
    └── ...
```

#### 1.2 部署选项
```javascript
// 部署方式
Deployment Options:
1. 本地文件系统 (file://)
2. 简单HTTP服务器 (python -m http.server)
3. 静态网站托管 (GitHub Pages, Netlify)
4. CDN分发 (CloudFlare, AWS CloudFront)
5. 企业内网部署
```

### 2. 配置管理

#### 2.1 环境配置
```javascript
// 环境配置管理
Environment Configuration:
1. 开发环境配置
2. 测试环境配置
3. 生产环境配置
4. 配置文件分离
5. 敏感信息保护
```

#### 2.2 版本管理
```javascript
// 版本控制策略
Version Control:
1. 语义化版本号
2. 变更日志维护
3. 向后兼容性
4. 升级路径规划
5. 回滚机制
```

## 技术债务管理

### 1. 代码质量

#### 1.1 代码规范
```javascript
// 代码质量标准
Code Quality Standards:
1. ESLint规则配置
2. 代码格式化标准
3. 注释规范
4. 命名约定
5. 文件组织规范
```

#### 1.2 重构计划
```javascript
// 重构优先级
Refactoring Priorities:
1. 性能瓶颈优化
2. 代码重复消除
3. 复杂度降低
4. 可维护性提升
5. 安全性加强
```

### 2. 文档维护

#### 2.1 文档更新策略
```javascript
// 文档维护策略
Documentation Maintenance:
1. 代码变更同步更新文档
2. 定期文档审查
3. 用户反馈整合
4. 版本文档管理
5. 多语言文档支持
```

#### 2.2 知识管理
```javascript
// 知识管理体系
Knowledge Management:
1. 技术决策记录
2. 最佳实践总结
3. 问题解决方案库
4. 经验教训记录
5. 团队知识共享
```