# 测试计划 - OTA订单处理系统

## 测试概述

本测试计划旨在确保OTA订单处理系统的功能完整性、性能稳定性和用户体验质量。测试覆盖功能测试、集成测试、性能测试、安全测试和用户体验测试。

## 测试策略

### 1. 测试目标
- **功能完整性**: 验证所有功能模块按预期工作
- **数据准确性**: 确保AI处理结果的准确性和一致性
- **系统稳定性**: 验证系统在各种条件下的稳定运行
- **用户体验**: 确保界面友好、操作流畅
- **安全性**: 验证数据安全和隐私保护
- **兼容性**: 确保跨浏览器和设备兼容性

### 2. 测试范围

#### 2.1 功能测试范围
- 用户认证系统
- 订单信息输入（文字/图片）
- OTA类型识别
- AI智能处理
- 结果预览和编辑
- 订单创建和提交
- 数据存储和缓存
- 错误处理和恢复

#### 2.2 非功能测试范围
- 性能测试（响应时间、并发处理）
- 兼容性测试（浏览器、设备）
- 安全测试（数据保护、输入验证）
- 可用性测试（用户体验、可访问性）

### 3. 测试环境

#### 3.1 浏览器环境
- **Chrome**: 最新版本及前两个版本
- **Firefox**: 最新版本及前两个版本
- **Safari**: 最新版本（macOS）
- **Edge**: 最新版本

#### 3.2 设备环境
- **桌面**: Windows 10/11, macOS 12+, Ubuntu 20.04+
- **平板**: iPad (iOS 15+), Android 平板 (Android 10+)
- **手机**: iPhone (iOS 15+), Android 手机 (Android 10+)

#### 3.3 网络环境
- 高速网络 (100Mbps+)
- 中速网络 (10-50Mbps)
- 低速网络 (2-10Mbps)
- 不稳定网络（模拟网络中断）

## 功能测试用例

### 1. 用户认证测试

#### TC001: 用户登录 - 成功场景
**测试目标**: 验证用户能够成功登录系统

**前置条件**: 
- 系统已启动
- 用户拥有有效的登录凭据

**测试步骤**:
1. 打开系统首页
2. 点击登录按钮
3. 输入有效的邮箱地址
4. 输入正确的密码
5. 点击登录按钮

**预期结果**:
- 登录成功，跳转到主界面
- 显示用户信息
- 登录状态持久化

**测试数据**:
```json
{
  "email": "<EMAIL>",
  "password": "Test123456"
}
```

#### TC002: 用户登录 - 失败场景
**测试目标**: 验证无效凭据的错误处理

**测试步骤**:
1. 输入无效邮箱或密码
2. 点击登录按钮

**预期结果**:
- 显示错误提示信息
- 不允许进入主界面
- 表单字段标记为错误状态

**测试数据**:
```json
[
  {"email": "<EMAIL>", "password": "wrongpass"},
  {"email": "<EMAIL>", "password": "wrongpass"},
  {"email": "invalid-email", "password": "Test123456"},
  {"email": "", "password": "Test123456"},
  {"email": "<EMAIL>", "password": ""}
]
```

#### TC003: 登录状态持久化
**测试目标**: 验证登录状态在页面刷新后保持

**测试步骤**:
1. 成功登录系统
2. 刷新页面
3. 关闭浏览器重新打开

**预期结果**:
- 页面刷新后仍保持登录状态
- 重新打开浏览器后需要重新登录（会话过期）

#### TC004: 用户登出
**测试目标**: 验证用户能够正常登出

**测试步骤**:
1. 在已登录状态下点击登出按钮
2. 确认登出操作

**预期结果**:
- 成功登出，返回登录界面
- 清除本地存储的认证信息
- 后续操作需要重新登录

### 2. 文字输入测试

#### TC005: 文字输入 - 基本功能
**测试目标**: 验证文字输入的基本功能

**测试步骤**:
1. 选择"文字输入"标签页
2. 在文本框中输入订单信息
3. 验证字符计数显示
4. 验证输入长度限制

**预期结果**:
- 文本正确显示在输入框中
- 字符计数实时更新
- 超出长度限制时显示警告

**测试数据**:
```
客户姓名：张三
联系电话：+60123456789
航班信息：MH123 - 明天14:30到达
接机地点：吉隆坡机场
目的地：希尔顿酒店
乘客人数：2人
备注：客户要求英文司机
```

#### TC006: 文字输入 - 边界值测试
**测试目标**: 验证文字输入的边界值处理

**测试数据**:
- 空输入
- 单字符输入
- 最大长度输入（10000字符）
- 超长输入（10001字符）
- 特殊字符输入
- 多语言输入（中文、英文、数字、符号）

**预期结果**:
- 空输入时显示提示信息
- 超长输入时截断或警告
- 特殊字符正确处理
- 多语言文本正确显示

#### TC007: 文字输入 - 格式验证
**测试目标**: 验证输入内容的格式验证

**测试步骤**:
1. 输入包含HTML标签的文本
2. 输入包含JavaScript代码的文本
3. 输入包含SQL注入尝试的文本

**预期结果**:
- 危险内容被过滤或转义
- 系统不受恶意输入影响
- 显示安全警告（如适用）

### 3. 图片上传测试

#### TC008: 图片上传 - 支持格式
**测试目标**: 验证支持的图片格式能够正常上传

**测试数据**:
- JPEG格式图片（.jpg, .jpeg）
- PNG格式图片（.png）
- WebP格式图片（.webp）
- HEIC格式图片（.heic）
- HEIF格式图片（.heif）

**测试步骤**:
1. 选择"图片上传"标签页
2. 点击上传按钮
3. 选择不同格式的图片文件
4. 确认上传

**预期结果**:
- 支持的格式成功上传
- 图片预览正确显示
- 文件信息正确显示

#### TC009: 图片上传 - 不支持格式
**测试目标**: 验证不支持格式的错误处理

**测试数据**:
- GIF格式图片（.gif）
- BMP格式图片（.bmp）
- SVG格式图片（.svg）
- PDF文件（.pdf）
- 文本文件（.txt）

**预期结果**:
- 显示格式不支持的错误信息
- 不允许上传
- 提示支持的格式列表

#### TC010: 图片上传 - 文件大小限制
**测试目标**: 验证文件大小限制

**测试数据**:
- 小文件（< 1MB）
- 中等文件（1-10MB）
- 大文件（10-20MB）
- 超大文件（> 20MB）

**预期结果**:
- 符合大小限制的文件成功上传
- 超大文件显示错误信息
- 显示文件大小限制说明

#### TC011: 图片上传 - 拖拽功能
**测试目标**: 验证拖拽上传功能

**测试步骤**:
1. 从文件管理器拖拽图片到上传区域
2. 释放鼠标
3. 验证上传结果

**预期结果**:
- 拖拽过程中显示视觉反馈
- 释放后开始上传
- 上传成功后显示预览

### 4. OTA识别测试

#### TC012: OTA自动识别 - Chong Dealer
**测试目标**: 验证Chong Dealer类型的自动识别

**测试数据**:
```
重庆经销商订单
客户：李四
电话：023-12345678
从重庆江北机场到解放碑希尔顿酒店
明天15:30航班到达
```

**预期结果**:
- 正确识别为"Chong Dealer"类型
- 置信度 > 0.8
- 显示识别理由

#### TC013: OTA自动识别 - 其他类型
**测试目标**: 验证其他OTA类型的识别

**测试数据**:
```
Booking.com订单确认
预订号：BK123456789
客户：John Smith
从KLIA到Petronas Twin Towers
```

**预期结果**:
- 识别为"Auto Detect"或"Other"类型
- 提供合理的置信度
- 显示识别依据

#### TC014: OTA手动选择
**测试目标**: 验证手动选择OTA类型功能

**测试步骤**:
1. 输入订单信息
2. 手动选择OTA类型
3. 验证选择结果

**预期结果**:
- 能够覆盖自动识别结果
- 手动选择优先级更高
- 界面状态正确更新

### 5. AI处理测试

#### TC015: AI处理 - Chong Dealer规则
**测试目标**: 验证Chong Dealer特定处理规则

**测试数据**:
```
客户：张三
电话：138-0000-1234
明天下午2点半的航班MH123从北京到吉隆坡
需要从KLIA接到市中心的希尔顿酒店
2个人，行李比较多
```

**预期结果**:
- 日期转换："明天" → 具体日期
- 时间计算：14:30到达 + 1小时 = 15:30接机
- 电话标准化：+60138-0000-1234
- 地点标准化：英文标准名称
- 生成OTA参考号码

#### TC016: AI处理 - 数据验证
**测试目标**: 验证AI处理结果的数据完整性

**验证项目**:
- 必填字段完整性
- 数据格式正确性
- 逻辑一致性
- 时间合理性

**预期结果**:
- 所有必填字段都有值
- 日期格式为YYYY-MM-DD
- 时间格式为HH:MM
- 电话号码符合国际格式

#### TC017: AI处理 - 错误处理
**测试目标**: 验证AI处理失败时的错误处理

**测试场景**:
- API密钥无效
- 网络连接失败
- API响应超时
- 返回数据格式错误

**预期结果**:
- 显示友好的错误信息
- 提供重试选项
- 不影响其他功能
- 记录错误日志

### 6. 结果编辑测试

#### TC018: 结果编辑 - 字段修改
**测试目标**: 验证处理结果的编辑功能

**测试步骤**:
1. 完成AI处理获得结果
2. 修改各个字段的值
3. 验证修改结果

**预期结果**:
- 所有字段都可以编辑
- 修改实时保存
- 验证规则正确应用
- 显示修改状态

#### TC019: 结果编辑 - 数据验证
**测试目标**: 验证编辑时的数据验证

**测试数据**:
- 无效电话号码格式
- 过去的日期
- 无效时间格式
- 空的必填字段

**预期结果**:
- 实时显示验证错误
- 阻止提交无效数据
- 提供修正建议
- 高亮错误字段

#### TC020: 结果编辑 - 撤销功能
**测试目标**: 验证编辑撤销功能

**测试步骤**:
1. 修改多个字段
2. 点击撤销按钮
3. 验证恢复结果

**预期结果**:
- 恢复到AI处理的原始结果
- 清除所有用户修改
- 重置验证状态

### 7. 订单创建测试

#### TC021: 订单创建 - 成功场景
**测试目标**: 验证订单成功创建

**前置条件**:
- 用户已登录
- 系统数据已加载
- 订单信息完整有效

**测试步骤**:
1. 完成订单信息处理和编辑
2. 点击"创建订单"按钮
3. 等待API响应

**预期结果**:
- 显示创建成功消息
- 返回订单号
- 提供后续操作选项
- 清空当前订单数据

#### TC022: 订单创建 - 失败场景
**测试目标**: 验证订单创建失败的处理

**测试场景**:
- 网络连接失败
- API服务不可用
- 数据验证失败
- 权限不足

**预期结果**:
- 显示具体错误信息
- 保留订单数据
- 提供重试选项
- 不清空用户输入

#### TC023: 订单创建 - 数据映射
**测试目标**: 验证UI数据到API数据的正确映射

**验证项目**:
- 字段名称映射
- 数据类型转换
- 必填字段补充
- 默认值设置

**预期结果**:
- API请求数据格式正确
- 所有必填字段都有值
- 数据类型符合API要求
- 默认值正确设置

## 集成测试用例

### 8. 端到端流程测试

#### TC024: 完整流程 - 文字输入
**测试目标**: 验证从文字输入到订单创建的完整流程

**测试步骤**:
1. 用户登录
2. 选择文字输入
3. 输入订单信息
4. AI处理
5. 编辑结果
6. 创建订单
7. 用户登出

**预期结果**:
- 整个流程顺畅无阻
- 数据在各步骤间正确传递
- 最终成功创建订单

#### TC025: 完整流程 - 图片输入
**测试目标**: 验证从图片上传到订单创建的完整流程

**测试步骤**:
1. 用户登录
2. 选择图片上传
3. 上传订单图片
4. AI处理图片
5. 编辑结果
6. 创建订单
7. 用户登出

**预期结果**:
- 图片正确识别和处理
- 提取的信息准确
- 成功创建订单

### 9. API集成测试

#### TC026: Gemini API集成
**测试目标**: 验证与Gemini AI API的集成

**测试场景**:
- 文本处理API调用
- 图片处理API调用
- API错误处理
- 响应数据解析

**预期结果**:
- API调用成功
- 响应数据格式正确
- 错误情况正确处理
- 超时机制有效

#### TC027: GoMyHire API集成
**测试目标**: 验证与GoMyHire API的集成

**测试场景**:
- 用户登录API
- 数据获取API（用户、分类、车型）
- 订单创建API
- 认证token管理

**预期结果**:
- 所有API调用正常
- 数据正确获取和显示
- Token自动管理
- 权限验证有效

## 性能测试用例

### 10. 响应时间测试

#### TC028: 页面加载性能
**测试目标**: 验证页面加载性能

**测试指标**:
- 首次内容绘制（FCP）< 1.5秒
- 最大内容绘制（LCP）< 2.5秒
- 首次输入延迟（FID）< 100毫秒
- 累积布局偏移（CLS）< 0.1

**测试环境**:
- 不同网络速度
- 不同设备性能
- 不同浏览器

#### TC029: API响应时间
**测试目标**: 验证API调用的响应时间

**测试指标**:
- 登录API < 2秒
- 数据获取API < 3秒
- AI处理API < 10秒
- 订单创建API < 5秒

#### TC030: 并发处理能力
**测试目标**: 验证系统的并发处理能力

**测试场景**:
- 多用户同时登录
- 多个AI处理请求
- 多个订单创建请求

**预期结果**:
- 系统稳定运行
- 响应时间在可接受范围
- 无数据冲突

## 兼容性测试用例

### 11. 浏览器兼容性

#### TC031: Chrome浏览器测试
**测试版本**: 最新版本及前两个版本
**测试内容**: 所有功能模块
**预期结果**: 功能完全正常

#### TC032: Firefox浏览器测试
**测试版本**: 最新版本及前两个版本
**测试内容**: 所有功能模块
**预期结果**: 功能完全正常

#### TC033: Safari浏览器测试
**测试版本**: macOS最新版本
**测试内容**: 所有功能模块
**预期结果**: 功能完全正常

#### TC034: Edge浏览器测试
**测试版本**: 最新版本
**测试内容**: 所有功能模块
**预期结果**: 功能完全正常

### 12. 设备兼容性

#### TC035: 桌面设备测试
**测试设备**: 
- Windows 10/11 PC
- macOS 12+ Mac
- Ubuntu 20.04+ Linux

**测试内容**: 完整功能测试
**预期结果**: 所有功能正常工作

#### TC036: 移动设备测试
**测试设备**:
- iPhone (iOS 15+)
- Android手机 (Android 10+)
- iPad (iOS 15+)
- Android平板 (Android 10+)

**测试内容**: 响应式布局和触摸交互
**预期结果**: 界面适配良好，交互流畅

## 安全测试用例

### 13. 输入验证测试

#### TC037: XSS攻击防护
**测试目标**: 验证跨站脚本攻击防护

**测试数据**:
```html
<script>alert('XSS')</script>
<img src="x" onerror="alert('XSS')">
<svg onload="alert('XSS')">
javascript:alert('XSS')
```

**预期结果**:
- 恶意脚本被过滤或转义
- 不执行恶意代码
- 系统安全不受影响

#### TC038: SQL注入防护
**测试目标**: 验证SQL注入攻击防护

**测试数据**:
```sql
'; DROP TABLE users; --
' OR '1'='1
' UNION SELECT * FROM users --
```

**预期结果**:
- 输入被正确处理
- 不影响后端数据库
- 显示安全错误信息

#### TC039: 文件上传安全
**测试目标**: 验证文件上传的安全性

**测试数据**:
- 恶意可执行文件
- 超大文件
- 文件名包含特殊字符
- 双重扩展名文件

**预期结果**:
- 只允许安全的图片格式
- 文件大小限制有效
- 文件名正确处理
- 恶意文件被拒绝

### 14. 数据保护测试

#### TC040: 敏感数据保护
**测试目标**: 验证敏感数据的保护

**测试内容**:
- API密钥不在前端暴露
- 用户密码不明文存储
- 个人信息加密传输
- 本地存储数据安全

**预期结果**:
- 敏感数据得到适当保护
- 传输过程使用HTTPS
- 本地存储数据加密

## 可用性测试用例

### 15. 用户体验测试

#### TC041: 界面友好性
**测试目标**: 验证界面的用户友好性

**测试内容**:
- 界面布局合理
- 颜色搭配舒适
- 字体大小适中
- 按钮位置合理
- 反馈信息清晰

**评估标准**:
- 用户能快速理解界面
- 操作流程直观
- 错误信息有帮助
- 成功操作有确认

#### TC042: 操作效率
**测试目标**: 验证操作的效率性

**测试内容**:
- 完成订单处理的步骤数
- 每个步骤的操作时间
- 键盘快捷键支持
- 批量操作支持

**评估标准**:
- 操作步骤最少化
- 响应时间快速
- 支持高效操作方式

### 16. 可访问性测试

#### TC043: 键盘导航
**测试目标**: 验证键盘导航功能

**测试内容**:
- Tab键导航顺序
- Enter键激活按钮
- Escape键关闭模态框
- 方向键导航标签页

**预期结果**:
- 所有交互元素可通过键盘访问
- 导航顺序逻辑合理
- 焦点指示清晰可见

#### TC044: 屏幕阅读器支持
**测试目标**: 验证屏幕阅读器支持

**测试工具**: NVDA, JAWS, VoiceOver

**测试内容**:
- 语义化HTML标签
- ARIA属性正确使用
- 图片alt文本
- 表单标签关联

**预期结果**:
- 屏幕阅读器能正确读取内容
- 交互元素有适当的描述
- 状态变化能被感知

## 测试执行计划

### 1. 测试阶段

#### 阶段1: 单元测试（开发阶段）
- **时间**: 开发过程中持续进行
- **范围**: 单个函数和组件
- **责任人**: 开发人员
- **工具**: Jest, Mocha, 浏览器开发者工具

#### 阶段2: 集成测试（开发完成后）
- **时间**: 1-2天
- **范围**: 模块间集成和API集成
- **责任人**: 开发人员和测试人员
- **工具**: Postman, 自动化测试脚本

#### 阶段3: 系统测试（集成测试完成后）
- **时间**: 3-5天
- **范围**: 完整系统功能
- **责任人**: 测试人员
- **工具**: 手动测试和自动化测试

#### 阶段4: 用户验收测试（系统测试完成后）
- **时间**: 2-3天
- **范围**: 用户场景和业务流程
- **责任人**: 业务用户和测试人员
- **工具**: 真实环境测试

### 2. 测试环境准备

#### 开发环境
- **用途**: 开发人员日常测试
- **数据**: 模拟数据
- **配置**: 开发配置

#### 测试环境
- **用途**: 正式测试执行
- **数据**: 测试数据集
- **配置**: 生产类似配置

#### 预生产环境
- **用途**: 最终验证
- **数据**: 生产数据副本
- **配置**: 生产配置

### 3. 测试数据准备

#### 用户账户数据
```json
[
  {
    "email": "<EMAIL>",
    "password": "Admin123456",
    "role": "admin"
  },
  {
    "email": "<EMAIL>",
    "password": "Operator123456",
    "role": "operator"
  },
  {
    "email": "<EMAIL>",
    "password": "Test123456",
    "role": "user"
  }
]
```

#### 订单测试数据
```json
[
  {
    "type": "chong_dealer",
    "content": "重庆经销商订单\n客户：张三\n电话：023-12345678\n明天15:30航班MH123从北京到重庆\n从江北机场到解放碑希尔顿酒店\n2人，行李较多"
  },
  {
    "type": "booking_com",
    "content": "Booking.com预订确认\n预订号：BK123456789\n客户：John Smith\n电话：+60123456789\n从KLIA到Petronas Twin Towers\n明天14:30到达"
  },
  {
    "type": "other",
    "content": "客户姓名：李四\n联系方式：+86138-0000-1234\n服务日期：2024-12-25\n服务时间：10:00\n上车地点：北京首都机场T3\n目的地：北京希尔顿酒店\n乘客数量：3人"
  }
]
```

#### 图片测试数据
- 清晰的订单截图（JPEG, PNG格式）
- 模糊的订单图片
- 包含多种信息的复杂图片
- 不同尺寸的图片文件
- 不同格式的图片文件

### 4. 缺陷管理

#### 缺陷分类
- **严重**: 系统崩溃、数据丢失、安全漏洞
- **高**: 主要功能无法使用
- **中**: 功能部分异常、性能问题
- **低**: 界面问题、文字错误

#### 缺陷报告模板
```
缺陷ID: BUG-001
标题: 登录失败后页面无响应
严重程度: 高
优先级: 高
发现环境: 测试环境
浏览器: Chrome 120.0
操作系统: Windows 11

重现步骤:
1. 打开登录页面
2. 输入错误的用户名密码
3. 点击登录按钮
4. 观察页面响应

预期结果: 显示错误信息，允许重新输入
实际结果: 页面无响应，按钮变灰无法点击

附件: 截图、日志文件
```

### 5. 测试报告

#### 测试执行报告
- 测试用例执行情况统计
- 通过率和失败率
- 缺陷发现和修复情况
- 测试覆盖率分析
- 性能测试结果
- 兼容性测试结果

#### 测试总结报告
- 测试目标达成情况
- 系统质量评估
- 风险评估和建议
- 发布建议
- 后续改进建议

## 自动化测试

### 1. 自动化测试框架

#### 单元测试框架
```javascript
// Jest测试示例
describe('DataValidator', () => {
    test('should validate phone number format', () => {
        expect(DataValidator.validatePhone('+60123456789')).toBe(true);
        expect(DataValidator.validatePhone('123456789')).toBe(false);
        expect(DataValidator.validatePhone('')).toBe(false);
    });
    
    test('should validate email format', () => {
        expect(DataValidator.validateEmail('<EMAIL>')).toBe(true);
        expect(DataValidator.validateEmail('invalid-email')).toBe(false);
    });
    
    test('should validate date range', () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowStr = tomorrow.toISOString().split('T')[0];
        
        expect(DataValidator.validateDate(tomorrowStr)).toBe(true);
        expect(DataValidator.validateDate('2020-01-01')).toBe(false);
    });
});
```

#### 端到端测试框架
```javascript
// Playwright测试示例
const { test, expect } = require('@playwright/test');

test.describe('OTA Order Processing', () => {
    test('complete order processing flow', async ({ page }) => {
        // 登录
        await page.goto('http://localhost:8000');
        await page.click('#loginBtn');
        await page.fill('#email', '<EMAIL>');
        await page.fill('#password', 'Test123456');
        await page.click('#loginSubmit');
        
        // 等待登录完成
        await expect(page.locator('#mainApp')).toBeVisible();
        
        // 输入订单信息
        await page.click('#textTab');
        await page.fill('#orderText', '客户：张三\n电话：+60123456789\n明天14:30航班到达');
        
        // 处理订单
        await page.click('#processBtn');
        
        // 等待AI处理完成
        await expect(page.locator('#resultPreview')).toBeVisible({ timeout: 15000 });
        
        // 验证结果
        const customerName = await page.locator('#customer_name').inputValue();
        expect(customerName).toBe('张三');
        
        const customerContact = await page.locator('#customer_contact').inputValue();
        expect(customerContact).toBe('+60123456789');
        
        // 创建订单
        await page.click('#createOrderBtn');
        
        // 验证成功消息
        await expect(page.locator('.notification-success')).toBeVisible();
    });
    
    test('image upload and processing', async ({ page }) => {
        await page.goto('http://localhost:8000');
        
        // 登录流程...
        
        // 切换到图片上传
        await page.click('#imageTab');
        
        // 上传图片
        const fileInput = page.locator('#imageInput');
        await fileInput.setInputFiles('test-data/order-screenshot.jpg');
        
        // 等待图片预览
        await expect(page.locator('#imagePreview')).toBeVisible();
        
        // 处理图片
        await page.click('#processBtn');
        
        // 验证处理结果
        await expect(page.locator('#resultPreview')).toBeVisible({ timeout: 20000 });
    });
});
```

### 2. 持续集成测试

#### GitHub Actions配置
```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
        browser: [chromium, firefox, webkit]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Install Playwright
      run: npx playwright install ${{ matrix.browser }}
    
    - name: Run E2E tests
      run: npm run test:e2e -- --project=${{ matrix.browser }}
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: test-results-${{ matrix.browser }}
        path: test-results/
```

这份测试计划文档详细描述了OTA订单处理系统的完整测试策略，包括功能测试、集成测试、性能测试、安全测试和可用性测试，为确保系统质量提供了全面的测试指南。