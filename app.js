/**
 * @file app.js - OTA订单处理系统主应用
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2024-12-19
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: 'https://staging.gomyhire.com.my/api',
    GEMINI_API_KEY: 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', // 请替换为实际的API Key
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    STORAGE_KEYS: {
        TOKEN: 'ota_system_token',
        USER_INFO: 'ota_system_user',
        BACKEND_USERS: 'ota_backend_users',
        SUB_CATEGORIES: 'ota_sub_categories',
        CAR_TYPES: 'ota_car_types'
    }
};

// 应用状态管理
class AppState {
    constructor() {
        this.token = localStorage.getItem(CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.backendUsers = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.processedOrders = [];
    }

    /**
     * @function setToken - 设置认证令牌
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem(CONFIG.STORAGE_KEYS.TOKEN, token);
    }

    /**
     * @function setUserInfo - 设置用户信息
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        this.userInfo = userInfo;
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
    }

    /**
     * @function clearAuth - 清除认证信息
     */
    clearAuth() {
        this.token = null;
        this.userInfo = null;
        localStorage.removeItem(CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_INFO);
    }

    /**
     * @function cacheSystemData - 缓存系统数据
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        this[key] = data;
        localStorage.setItem(CONFIG.STORAGE_KEYS[key.toUpperCase()], JSON.stringify(data));
    }
}

// API服务类
class ApiService {
    constructor(appState) {
        this.appState = appState;
    }

    /**
     * @function login - 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password) {
        logger.info('API', '发起登录请求', { email, url: `${CONFIG.API_BASE_URL}/login` });
        
        try {
            const requestData = {
                email: email,
                password: password
            };
            
            logger.debug('API', '登录请求数据', requestData);
            
            const response = await axios.post(`${CONFIG.API_BASE_URL}/login`, requestData);
            
            logger.debug('API', '登录响应数据', {
                status: response.status,
                data: response.data
            });
            
            if (response.data.status && response.data.token) {
                // 提取实际token（去掉数字|前缀）
                const actualToken = response.data.token.split('|')[1] || response.data.token;
                
                logger.debug('API', 'Token处理完成', {
                    originalToken: response.data.token.substring(0, 20) + '...',
                    processedToken: actualToken.substring(0, 20) + '...'
                });
                
                this.appState.setToken(actualToken);
                this.appState.setUserInfo(response.data.user || { email });
                
                logger.success('API', '登录API调用成功');
                
                return { success: true, token: actualToken };
            } else {
                logger.error('API', '登录响应格式错误', response.data);
                throw new Error('登录失败：无效的响应');
            }
        } catch (error) {
            logger.error('API', '登录API调用失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '登录失败，请检查网络连接');
        }
    }

    /**
     * @function getBackendUsers - 获取后台用户列表
     * @returns {Promise<array>} 用户列表
     */
    async getBackendUsers() {
        logger.info('API', '发起获取后端用户请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取后端用户失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送后端用户请求', {
                url: `${CONFIG.API_BASE_URL}/backend_users`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/backend_users`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            logger.debug('API', '后端用户响应数据', {
                status: response.status,
                dataLength: response.data?.data?.length || 0
            });
            
            const users = response.data.data || [];
            this.appState.cacheSystemData('backendUsers', users);
            
            logger.success('API', '获取后端用户成功');
            return users;
        } catch (error) {
            logger.error('API', '获取后端用户失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取后端用户失败');
        }
    }

    /**
     * @function getSubCategories - 获取子分类列表
     * @returns {Promise<array>} 子分类列表
     */
    async getSubCategories() {
        logger.info('API', '发起获取子分类请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取子分类失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送子分类请求', {
                url: `${CONFIG.API_BASE_URL}/sub_category`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/sub_category`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            logger.debug('API', '子分类响应数据', {
                status: response.status,
                dataLength: response.data?.data?.length || 0
            });
            
            const categories = response.data.data || [];
            this.appState.cacheSystemData('subCategories', categories);
            
            logger.success('API', '获取子分类成功');
            return categories;
        } catch (error) {
            logger.error('API', '获取子分类失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取子分类失败');
        }
    }

    /**
     * @function getCarTypes - 获取车型列表
     * @returns {Promise<array>} 车型列表
     */
    async getCarTypes() {
        logger.info('API', '发起获取车型请求');
        
        try {
            const token = this.appState.token;
            if (!token) {
                logger.error('API', '获取车型失败：未找到认证token');
                throw new Error('未找到认证token');
            }
            
            logger.debug('API', '发送车型请求', {
                url: `${CONFIG.API_BASE_URL}/car_types`,
                hasToken: !!token
            });
            
            const response = await axios.get(`${CONFIG.API_BASE_URL}/car_types`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                params: { search: '' }
            });
            
            logger.debug('API', '车型响应数据', {
                status: response.status,
                dataLength: response.data?.data?.length || 0
            });
            
            const carTypes = response.data.data || [];
            this.appState.cacheSystemData('carTypes', carTypes);
            
            logger.success('API', '获取车型成功');
            return carTypes;
        } catch (error) {
            logger.error('API', '获取车型失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw new Error(error.response?.data?.message || '获取车型失败');
        }
    }

    /**
     * @function createOrder - 创建订单
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 创建结果
     */
    async createOrder(orderData) {
        logger.info('API', '发起创建订单请求', {
            hasData: !!orderData,
            dataKeys: Object.keys(orderData || {})
        });
        
        try {
            logger.debug('API', '创建订单请求数据', {
                url: `${CONFIG.API_BASE_URL}/create_order`,
                orderData: {
                    ...orderData,
                    // 隐藏敏感信息
                    customer_phone: orderData.customer_phone ? '***' + orderData.customer_phone.slice(-4) : undefined
                }
            });
            
            const response = await axios.post(`${CONFIG.API_BASE_URL}/create_order`, orderData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            logger.debug('API', '创建订单响应数据', {
                status: response.status,
                hasData: !!response.data,
                responseKeys: Object.keys(response.data || {})
            });
            
            logger.success('API', '创建订单成功');
            return response.data;
        } catch (error) {
            logger.error('API', '创建订单失败', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                orderData: {
                    ...orderData,
                    // 隐藏敏感信息
                    customer_phone: orderData?.customer_phone ? '***' + orderData.customer_phone.slice(-4) : undefined
                }
            });
            throw new Error(error.response?.data?.message || '创建订单失败');
        }
    }
}

// Gemini AI服务类
class GeminiService {
    constructor() {
        // 初始化提示词管理器
        this.promptManager = new PromptManager();
    }

    /**
     * @function detectOTATypeLocally - 本地识别OTA类型
     * @param {string} orderContent - 订单内容
     * @returns {Object} 本地识别结果
     */
    detectOTATypeLocally(orderContent) {
        const content = orderContent.toLowerCase();
        
        // Chong Dealer 特征识别
        const chongFeatures = [
            /ota[：:].{0,5}chong/i,
            /chong.{0,5}车头/i,
            /chong.{0,5}dealer/i,
            /重庆.{0,5}经销商/i,
            /航班.{0,5}[a-z]{2}\d{3,4}/i,
            /接机.{0,10}\d{1,2}[.:]\d{2}/i,
            /送机.{0,10}\d{1,2}[.:]\d{2}/i
        ];
        
        let chongScore = 0;
        let detectedFeatures = [];
        
        // 检测 Chong Dealer 特征
        chongFeatures.forEach(pattern => {
            if (pattern.test(content)) {
                chongScore += 1;
                detectedFeatures.push('Chong Dealer特征');
            }
        });
        

        
        // 判断结果
        let otaType = 'auto';
        let confidence = 0;
        let reasoning = '未检测到明确的OTA特征';
        
        if (chongScore >= 2) {
            otaType = 'chong-dealer';
            confidence = Math.min(0.9, 0.5 + (chongScore * 0.1));
            reasoning = `检测到${chongScore}个Chong Dealer特征`;
        } else if (chongScore > 0) {
            otaType = 'chong-dealer';
            confidence = 0.4;
            reasoning = '检测到部分Chong Dealer特征，但不够明确';
        }
        
        return {
            ota_type: otaType,
            confidence: confidence,
            reasoning: reasoning,
            key_features: detectedFeatures,
            scores: { chong: chongScore }
        };
    }

    /**
     * @function processOrderWithAI - 使用AI处理订单
     * @param {string} orderContent - 订单内容
     * @param {string} otaType - OTA类型
     * @returns {Promise<string>} 处理结果
     */
    async processOrderWithAI(orderContent, otaType = 'chong-dealer') {
        logger.info('AI', '开始AI订单处理', {
            contentLength: orderContent?.length || 0,
            otaType: otaType,
            hasApiKey: !!CONFIG.GEMINI_API_KEY
        });
        
        try {
            // 使用提示词管理器获取对应的提示词
            const currentDate = new Date().toISOString().split('T')[0];
            logger.debug('AI', '获取提示词', {
                otaType: otaType,
                currentDate: currentDate
            });
            
            const prompt = this.promptManager.getOTAPrompt(otaType, orderContent, currentDate);
            
            logger.debug('AI', '构建Gemini请求', {
                promptLength: prompt?.length || 0,
                apiUrl: CONFIG.GEMINI_API_URL
            });
            
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }]
                    })
                }
            );
            
            logger.debug('AI', 'Gemini响应状态', {
                status: response.status,
                ok: response.ok
            });
            
            if (!response.ok) {
                logger.error('AI', 'Gemini API请求失败', {
                    status: response.status,
                    statusText: response.statusText
                });
                throw new Error(`Gemini API错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            logger.debug('AI', 'Gemini响应数据', {
                hasCandidates: !!data.candidates,
                candidatesLength: data.candidates?.length || 0
            });
            
            if (data.candidates && data.candidates[0]) {
                const result = data.candidates[0].content.parts[0].text;
                logger.success('AI', 'AI订单处理成功', {
                    resultLength: result?.length || 0
                });
                return result;
            } else {
                logger.error('AI', 'AI处理失败：无效响应', data);
                throw new Error('AI处理失败：无效响应');
            }
        } catch (error) {
            logger.error('AI', 'AI订单处理失败', {
                message: error.message,
                stack: error.stack,
                otaType: otaType
            });
            throw new Error('AI处理失败，请检查API配置或网络连接');
        }
    }

    /**
     * @function detectOTAType - 检测订单的OTA类型
     * @param {string} orderContent - 订单内容
     * @returns {Promise<Object>} OTA类型识别结果
     */
    async detectOTAType(orderContent) {
        logger.info('AI', '开始OTA类型识别', {
            contentLength: orderContent?.length || 0
        });
        
        try {
            const prompt = this.promptManager.getDetectionPrompt(orderContent);
            
            logger.debug('AI', '构建OTA识别请求', {
                promptLength: prompt?.length || 0
            });
            
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }]
                    })
                }
            );
            
            logger.debug('AI', 'OTA识别响应状态', {
                status: response.status,
                ok: response.ok
            });
            
            if (!response.ok) {
                logger.error('AI', 'OTA识别API请求失败', {
                    status: response.status,
                    statusText: response.statusText
                });
                throw new Error(`OTA识别API错误: ${response.status}`);
            }
            
            const data = await response.json();
            const result = data.candidates[0].content.parts[0].text;
            
            logger.debug('AI', 'OTA识别原始结果', {
                resultLength: result?.length || 0,
                resultPreview: result?.substring(0, 100) + '...'
            });
            
            // 尝试解析JSON结果
            try {
                const parsedResult = JSON.parse(result);
                logger.success('AI', 'OTA类型识别成功', {
                    otaType: parsedResult.ota_type,
                    confidence: parsedResult.confidence,
                    reasoning: parsedResult.reasoning
                });
                return parsedResult;
            } catch (parseError) {
                logger.warn('AI', 'OTA类型识别结果解析失败，使用默认值', {
                    parseError: parseError.message,
                    rawResult: result
                });
                return {
                    ota_type: 'auto',
                    confidence: 0.5,
                    reasoning: '自动识别失败，使用默认类型',
                    key_features: []
                };
            }
        } catch (error) {
            logger.error('AI', 'OTA类型识别失败', {
                message: error.message,
                stack: error.stack
            });
            return {
                ota_type: 'auto',
                confidence: 0,
                reasoning: '识别过程出错',
                key_features: []
            };
        }
    }

    /**
     * @function processImageWithAI - 使用AI处理图片订单
     * @param {string} imageData - 图片数据（base64）
     * @returns {Promise<string>} 处理后的订单信息
     */
    async processImageWithAI(imageData) {
        try {
            const currentDate = new Date().toISOString().split('T')[0];
            const prompt = this.promptManager.getImageOCRPrompt(currentDate);
            
            const response = await fetch(
                `${CONFIG.GEMINI_API_URL.replace('gemini-pro', 'gemini-pro-vision')}?key=${CONFIG.GEMINI_API_KEY}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                {
                                    text: prompt
                                },
                                {
                                    inline_data: {
                                        mime_type: 'image/jpeg',
                                        data: imageData
                                    }
                                }
                            ]
                        }]
                    })
                }
            );
            
            const data = await response.json();
            return data.candidates[0].content.parts[0].text;
        } catch (error) {
            console.error('图片AI处理失败:', error);
            throw new Error('图片处理失败，请重试');
        }
    }

    /**
     * @function getAvailablePromptTypes - 获取所有可用的提示词类型
     * @returns {Array} 提示词类型列表
     */
    getAvailablePromptTypes() {
        return this.promptManager.getAvailableTypes();
    }
}

// 图片处理服务
class ImageService {
    /**
     * @function extractTextFromImage - 从图片中提取文字（使用OCR）
     * @param {File} imageFile - 图片文件
     * @returns {Promise<string>} 提取的文字
     */
    async extractTextFromImage(imageFile) {
        // 这里可以集成OCR服务，如Google Vision API、Tesseract.js等
        // 暂时返回模拟数据
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve('模拟从图片提取的订单文字内容...');
            }, 2000);
        });
    }
}

// 主应用类
class OTAOrderApp {
    constructor() {
        logger.info('应用', 'OTAOrderApp 构造函数开始执行');
        try {
            this.appState = new AppState();
            this.apiService = new ApiService(this.appState);
            this.geminiService = new GeminiService();
            this.imageService = new ImageService();
            
            this.initializeApp();
            logger.success('应用', 'OTAOrderApp 构造完成');
        } catch (error) {
            logger.error('应用', 'OTAOrderApp 构造失败', error);
            throw error;
        }
    }

    /**
     * @function initializeApp - 初始化应用
     */
    initializeApp() {
        logger.info('应用', '开始初始化应用');
        try {
            // 绑定事件监听器
            logger.debug('初始化', '绑定事件监听器');
            this.bindEvents();
            
            // 检查认证状态
            logger.debug('初始化', '检查认证状态');
            this.checkAuthStatus();
            
            logger.success('应用', '应用初始化完成');
        } catch (error) {
            logger.error('应用', '应用初始化失败', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     */
    checkAuthStatus() {
        if (this.appState.token) {
            this.showMainApp();
            this.loadSystemData();
        } else {
            this.showLoginModal();
        }
    }

    /**
     * @function bindEvents - 绑定事件监听器
     */
    bindEvents() {
        // 登录表单
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 图片上传
        const uploadArea = document.getElementById('uploadArea');
        const imageFile = document.getElementById('imageFile');
        
        uploadArea.addEventListener('click', () => imageFile.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        imageFile.addEventListener('change', this.handleImageSelect.bind(this));

        // 处理订单
        document.getElementById('processBtn').addEventListener('click', () => {
            this.handleProcessOrder();
        });

        // 编辑结果
        document.getElementById('editBtn').addEventListener('click', () => {
            this.enableResultEditing();
        });

        // 重新处理
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.handleProcessOrder();
        });

        // 创建订单
        document.getElementById('createOrderBtn').addEventListener('click', () => {
            this.handleCreateOrders();
        });

        // 导出结果
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportResults();
        });
    }

    /**
     * @function handleLogin - 处理登录
     */
    async handleLogin() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');
        
        logger.info('认证', '开始登录流程', { email });
        
        if (!email || !password) {
            logger.warn('认证', '登录参数不完整');
            this.showError('请输入邮箱和密码');
            return;
        }
        
        try {
            this.showLoading('正在登录...');
            logger.debug('认证', '发送登录请求');
            
            const result = await this.apiService.login(email, password);
            logger.debug('认证', '收到登录响应', result);
            
            if (result.success) {
                logger.success('认证', '登录成功');
                this.hideLoading();
                this.showMainApp();
                this.loadSystemData();
            }
        } catch (error) {
            logger.error('认证', '登录过程发生错误', error);
            this.hideLoading();
            errorDiv.textContent = error.message;
        } finally {
            logger.debug('认证', '登录流程结束');
        }
    }

    /**
     * @function handleLogout - 处理退出登录
     */
    handleLogout() {
        this.appState.clearAuth();
        this.showLoginModal();
    }

    /**
     * @function loadSystemData - 加载系统数据
     */
    async loadSystemData() {
        try {
            this.showLoading('正在加载系统数据...');
            
            // 并行加载所有系统数据
            await Promise.all([
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ]);
            
            this.hideLoading();
            this.updateUserInfo();
        } catch (error) {
            this.hideLoading();
            console.error('加载系统数据失败:', error);
        }
    }

    /**
     * @function updateUserInfo - 更新用户信息显示
     */
    updateUserInfo() {
        const userInfoElement = document.getElementById('userInfo');
        userInfoElement.textContent = `已登录 | 后台用户: ${this.appState.backendUsers.length} | 子分类: ${this.appState.subCategories.length} | 车型: ${this.appState.carTypes.length}`;
    }

    /**
     * @function switchTab - 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Input`).classList.add('active');
    }

    /**
     * @function handleDragOver - 处理拖拽悬停
     * @param {Event} e - 事件对象
     */
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    /**
     * @function handleDrop - 处理文件拖拽放置
     * @param {Event} e - 事件对象
     */
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files).filter(file => 
            file.type.startsWith('image/')
        );
        
        this.processImages(files);
    }

    /**
     * @function handleImageSelect - 处理图片选择
     * @param {Event} e - 事件对象
     */
    handleImageSelect(e) {
        const files = Array.from(e.target.files);
        this.processImages(files);
    }

    /**
     * @function processImages - 处理图片文件
     * @param {File[]} files - 图片文件数组
     */
    processImages(files) {
        const previewContainer = document.getElementById('imagePreview');
        
        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                imageItem.innerHTML = `
                    <img src="${e.target.result}" alt="上传的图片">
                    <button class="image-remove" onclick="this.parentElement.remove()">×</button>
                `;
                previewContainer.appendChild(imageItem);
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * @function handleProcessOrder - 处理订单
     */
    async handleProcessOrder() {
        logger.info('订单处理', '开始处理订单流程');
        
        try {
            this.showLoading('正在处理订单...');
            
            // 获取订单内容
            let orderContent = '';
            const activeTab = document.querySelector('.tab-btn.active').dataset.tab;
            
            logger.debug('订单处理', '获取订单内容', {
                activeTab: activeTab
            });
            
            if (activeTab === 'text') {
                orderContent = document.getElementById('orderText').value.trim();
                logger.debug('订单处理', '文本输入模式', {
                    contentLength: orderContent.length
                });
            } else if (activeTab === 'image') {
                // 从图片中提取文字
                const images = document.querySelectorAll('#imagePreview img');
                logger.debug('订单处理', '图片输入模式', {
                    imageCount: images.length
                });
                
                if (images.length === 0) {
                    logger.error('订单处理', '图片输入模式但未上传图片');
                    throw new Error('请先上传图片');
                }
                // 这里应该调用OCR服务提取文字
                orderContent = '从图片提取的订单内容...';
                logger.warn('订单处理', '使用模拟OCR内容', {
                    content: orderContent
                });
            }
            
            if (!orderContent) {
                logger.error('订单处理', '订单内容为空');
                throw new Error('请输入订单内容');
            }
            
            logger.info('订单处理', '订单内容获取成功', {
                contentLength: orderContent.length,
                inputMode: activeTab
            });
            
            // 获取OTA类型
        let otaType = document.getElementById('otaSelect').value;
        
        logger.debug('订单处理', '获取OTA类型设置', {
            selectedOtaType: otaType
        });
        
        // 如果选择了自动识别，先进行本地OTA类型检测
        if (otaType === 'auto') {
            logger.info('订单处理', '开始自动OTA类型识别');
            this.showStatus('正在本地识别OTA类型...', 'info');
            
            // 第一步：本地特征识别
            const localDetection = this.geminiService.detectOTATypeLocally(orderContent);
            logger.debug('订单处理', '本地OTA类型识别结果', localDetection);
            
            // 如果本地识别置信度足够高，直接使用
            if (localDetection.confidence >= 0.6) {
                otaType = localDetection.ota_type;
                logger.success('订单处理', '本地OTA识别置信度足够，直接使用', {
                    otaType: otaType,
                    confidence: localDetection.confidence,
                    reasoning: localDetection.reasoning
                });
                this.showStatus(`本地识别为${localDetection.ota_type}类型 (置信度: ${Math.round(localDetection.confidence * 100)}%) - ${localDetection.reasoning}`, 'success');
            } else {
                // 第二步：本地识别置信度不够，使用Gemini AI进行深度识别
                logger.info('订单处理', '本地识别置信度不够，启用AI深度识别', {
                    localConfidence: localDetection.confidence
                });
                this.showStatus('本地识别置信度不够，正在使用AI深度识别...', 'info');
                try {
                    const aiDetection = await this.geminiService.detectOTAType(orderContent);
                    logger.debug('订单处理', 'AI OTA类型识别结果', aiDetection);
                    
                    // 综合本地识别和AI识别结果
                    if (aiDetection.confidence > 0.7) {
                        otaType = aiDetection.ota_type;
                        logger.success('订单处理', 'AI识别置信度高，使用AI结果', {
                            otaType: otaType,
                            aiConfidence: aiDetection.confidence
                        });
                        this.showStatus(`AI识别为${aiDetection.ota_type}类型 (置信度: ${Math.round(aiDetection.confidence * 100)}%)`, 'success');
                    } else if (localDetection.confidence > 0.3) {
                        // AI识别也不确定，但本地有一定置信度，使用本地结果
                        otaType = localDetection.ota_type;
                        logger.warn('订单处理', 'AI识别不确定，回退到本地识别结果', {
                            localOtaType: localDetection.ota_type,
                            localConfidence: localDetection.confidence,
                            aiConfidence: aiDetection.confidence
                        });
                        this.showStatus(`使用本地识别结果: ${localDetection.ota_type} (AI识别不确定)`, 'warning');
                    } else {
                        // 都不确定，使用通用模式
                        logger.warn('订单处理', '本地和AI识别都不确定，使用通用模式', {
                            localConfidence: localDetection.confidence,
                            aiConfidence: aiDetection.confidence
                        });
                        this.showStatus('无法确定OTA类型，使用通用处理模式', 'warning');
                        otaType = 'auto';
                    }
                } catch (error) {
                    logger.error('订单处理', 'AI OTA类型识别失败', {
                        error: error.message,
                        localConfidence: localDetection.confidence
                    });
                    if (localDetection.confidence > 0.3) {
                        otaType = localDetection.ota_type;
                        logger.warn('订单处理', 'AI识别失败，回退到本地识别', {
                            otaType: otaType
                        });
                        this.showStatus(`AI识别失败，使用本地识别结果: ${localDetection.ota_type}`, 'warning');
                    } else {
                        logger.warn('订单处理', 'AI和本地识别都失败，使用通用模式');
                        this.showStatus('OTA类型识别失败，使用通用处理模式', 'warning');
                        otaType = 'auto';
                    }
                }
            }
        }
        
        // 使用AI处理订单
        logger.info('订单处理', '开始AI订单处理', {
            finalOtaType: otaType,
            contentLength: orderContent.length
        });
        this.showStatus(`正在使用${otaType}模式处理订单...`, 'info');
        const processedResult = await this.geminiService.processOrderWithAI(orderContent, otaType);
            
            // 解析处理结果，传递实际的OTA类型
            logger.debug('订单处理', '开始解析AI处理结果', {
                resultLength: processedResult?.length || 0,
                otaType: otaType
            });
            this.appState.processedOrders = this.parseProcessedOrders(processedResult, otaType);
            
            logger.success('订单处理', '订单处理流程完成', {
                processedOrdersCount: this.appState.processedOrders?.length || 0
            });
            
            this.hideLoading();
            this.showProcessedResults(processedResult);
            
        } catch (error) {
            logger.error('订单处理', '订单处理流程失败', {
                error: error.message,
                stack: error.stack
            });
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * @function parseProcessedOrders - 解析处理后的订单
     * @param {string} processedText - 处理后的文本
     * @param {string} actualOtaType - 实际的OTA类型
     * @returns {array} 解析后的订单数组
     */
    parseProcessedOrders(processedText, actualOtaType = null) {
        // 简单的解析逻辑，实际应该更复杂
        const orders = [];
        const orderBlocks = processedText.split('\n\n').filter(block => block.trim());
        
        // 获取OTA类型，优先使用传入的实际类型
        const otaType = actualOtaType || document.getElementById('otaSelect').value;
        
        orderBlocks.forEach((block, index) => {
            const lines = block.split('\n');
            const order = {
                id: index + 1,
                raw: block,
                parsed: {},
                ota_type: this.mapOtaType(otaType) // 设置OTA类型
            };
            
            lines.forEach(line => {
                const [key, value] = line.split(':').map(s => s.trim());
                if (key && value) {
                    order.parsed[key] = value;
                }
            });
            
            orders.push(order);
        });
        
        return orders;
    }

    /**
     * @function mapOtaType - 映射OTA类型
     * @param {string} selectedType - 选择的类型
     * @returns {string} 映射后的类型
     */
    mapOtaType(selectedType) {
        switch (selectedType) {
            case 'chong-dealer':
                return 'Chong Dealer';
            case 'auto':
                // 对于自动识别，需要从实际识别结果中获取
                // 这里暂时返回通用类型，实际应该从识别结果中获取
                return 'Auto';
            default:
                return 'Other';
        }
    }

    /**
     * @function showProcessedResults - 显示处理结果
     * @param {string} results - 处理结果
     */
    showProcessedResults(results) {
        const resultContent = document.getElementById('resultContent');
        resultContent.textContent = results;
        
        document.getElementById('resultPreview').classList.remove('hidden');
        document.getElementById('orderStatus').classList.add('hidden');
    }

    /**
     * @function enableResultEditing - 启用结果编辑
     */
    enableResultEditing() {
        const resultContent = document.getElementById('resultContent');
        const currentContent = resultContent.textContent;
        
        resultContent.innerHTML = `<textarea style="width: 100%; min-height: 300px; font-family: 'Courier New', monospace;">${currentContent}</textarea>`;
        
        const textarea = resultContent.querySelector('textarea');
        textarea.addEventListener('blur', () => {
            resultContent.textContent = textarea.value;
            // 重新解析订单
            this.appState.processedOrders = this.parseProcessedOrders(textarea.value);
        });
    }

    /**
     * @function handleCreateOrders - 处理创建订单
     */
    async handleCreateOrders() {
        if (this.appState.processedOrders.length === 0) {
            this.showError('没有可创建的订单');
            return;
        }
        
        try {
            this.showLoading('正在创建订单...');
            
            const statusContent = document.getElementById('statusContent');
            statusContent.innerHTML = '';
            
            // 为每个订单创建API调用
            const createPromises = this.appState.processedOrders.map(async (order, index) => {
                try {
                    // 构建订单数据
                    const orderData = this.buildOrderData(order);
                    
                    // 调用创建订单API
                    const result = await this.apiService.createOrder(orderData);
                    
                    // 显示成功状态
                    this.addStatusItem(`订单 ${index + 1}`, '创建成功', 'success', result);
                    
                    return { success: true, order, result };
                } catch (error) {
                    // 显示失败状态
                    this.addStatusItem(`订单 ${index + 1}`, `创建失败: ${error.message}`, 'error');
                    return { success: false, order, error };
                }
            });
            
            await Promise.all(createPromises);
            
            this.hideLoading();
            document.getElementById('orderStatus').classList.remove('hidden');
            
        } catch (error) {
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * @function buildOrderData - 构建订单数据
     * @param {object} order - 订单对象
     * @returns {object} API所需的订单数据
     */
    buildOrderData(order) {
        // 根据解析的订单数据构建API所需格式
        const data = {
            sub_category_id: this.getSubCategoryId(),
            car_type_id: this.getCarTypeId(),
            incharge_by_backend_user_id: this.getBackendUserId(),
            ota_reference_number: this.generateOTAReference(order),
            customer_name: order.parsed['姓名'] || order.parsed['客人姓名'] || order.parsed['联系人'] || '',
            customer_contact: order.parsed['电话'] || this.generateRandomContact(order),
            customer_email: order.parsed['邮箱'] || '<EMAIL>',
            flight_info: order.parsed['航班'] || '',
            pickup: order.parsed['pickup'] || order.parsed['上车地点'] || '',
            destination: order.parsed['drop'] || order.parsed['下车地点'] || '',
            date: order.parsed['日期'] || '',
            time: order.parsed['时间'] || '',
            passenger_number: order.parsed['人数'] || 1,
            luggage_number: order.parsed['行李数'] || 1,
            driver_fee: order.parsed['司机费'] || 1,
            ota_price: order.parsed['价格'] || 0,
            extra_requirement: order.parsed['备注'] || order.parsed['other'] || '⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单'
        };
        
        // 根据OTA类型应用默认预设值
        if (order.ota_type === 'Chong Dealer') {
            data.ota = 'Chong Dealer';
            data.ota_price = 0;
            data.customer_email = '<EMAIL>';
            data.passenger_number = 1;
            data.luggage_number = 1;
            data.driver_fee = 1;
            data.extra_requirement = '⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单';
        }
        
        return data;
    }

    /**
     * @function getSubCategoryId - 获取子分类ID
     * @returns {number} 子分类ID
     */
    getSubCategoryId() {
        return this.appState.subCategories.length > 0 ? this.appState.subCategories[0].id : 1;
    }

    /**
     * @function getCarTypeId - 获取车型ID
     * @returns {number} 车型ID
     */
    getCarTypeId() {
        return this.appState.carTypes.length > 0 ? this.appState.carTypes[0].id : 1;
    }

    /**
     * @function getBackendUserId - 获取后台用户ID
     * @returns {number} 后台用户ID
     */
    getBackendUserId() {
        return this.appState.backendUsers.length > 0 ? this.appState.backendUsers[0].id : 1;
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @param {object} order - 订单对象
     * @returns {string} OTA参考号
     */
    generateOTAReference(order) {
        // 获取订单信息
        const orderDate = order.parsed?.['日期'] || new Date().toISOString().slice(0, 10);
        const orderTime = order.parsed?.['时间'] || new Date().toTimeString().slice(0, 5);
        const flightInfo = order.parsed?.['航班'] || '';
        const customerName = order.parsed?.['姓名'] || order.parsed?.['客人姓名'] || order.parsed?.['联系人'] || '';
        
        // 格式化日期时间
        const dateFormatted = orderDate.replace(/-/g, '');
        const timeFormatted = orderTime.replace(':', '');
        
        // 生成唯一标识符
        const uniqueId = `${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}`;
        
        return uniqueId;
    }

    /**
     * @function generateRandomContact - 生成随机联系方式
     * @param {object} order - 订单对象
     * @returns {string} 随机联系方式
     */
    generateRandomContact(order) {
        // 基于订单信息生成随机联系方式
        const orderDate = order.parsed?.['日期'] || new Date().toISOString().slice(0, 10);
        const orderTime = order.parsed?.['时间'] || new Date().toTimeString().slice(0, 5);
        const flightInfo = order.parsed?.['航班'] || '';
        const customerName = order.parsed?.['姓名'] || order.parsed?.['客人姓名'] || order.parsed?.['联系人'] || '';
        
        // 格式化并生成联系方式
        const dateFormatted = orderDate.replace(/-/g, '');
        const timeFormatted = orderTime.replace(':', '');
        
        return `${dateFormatted}${timeFormatted}-${flightInfo}-${customerName}`;
    }

    /**
     * @function addStatusItem - 添加状态项
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {string} type - 类型（success/error/pending）
     * @param {object} data - 附加数据
     */
    addStatusItem(title, message, type, data = null) {
        const statusContent = document.getElementById('statusContent');
        const statusItem = document.createElement('div');
        statusItem.className = `status-item ${type}`;
        
        let content = `<h4>${title}</h4><p>${message}</p>`;
        if (data) {
            content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        statusItem.innerHTML = content;
        statusContent.appendChild(statusItem);
    }

    /**
     * @function exportResults - 导出结果
     */
    exportResults() {
        const resultContent = document.getElementById('resultContent').textContent;
        const blob = new Blob([resultContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ota_orders_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * @function showLoginModal - 显示登录模态框
     */
    showLoginModal() {
        document.getElementById('loginModal').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
    }

    /**
     * @function showMainApp - 显示主应用
     */
    showMainApp() {
        document.getElementById('loginModal').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
    }

    /**
     * @function showLoading - 显示加载提示
     * @param {string} text - 加载文本
     */
    showLoading(text) {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingModal').classList.remove('hidden');
    }

    /**
     * @function hideLoading - 隐藏加载提示
     */
    hideLoading() {
        document.getElementById('loadingModal').classList.add('hidden');
    }

    /**
     * @function showError - 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        alert(`错误: ${message}`);
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    // 启动日志系统
    logger.info('系统', '应用开始初始化...');
    
    try {
        // 创建应用实例
        logger.debug('初始化', '创建应用实例');
        new OTAOrderApp();
        
        logger.success('系统', '应用初始化完成');
    } catch (error) {
        logger.error('初始化', '应用初始化失败', error);
    }
});