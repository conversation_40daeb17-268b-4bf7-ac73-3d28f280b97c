# OTA订单处理系统性能优化指南

## 概述

本文档提供了OTA订单处理系统的性能优化策略，包括前端性能优化、API调用优化、缓存策略、监控指标和性能测试等方面的最佳实践。

## 🚀 前端性能优化

### 1. 资源加载优化

```html
<!-- 在index.html中优化资源加载 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//generativelanguage.googleapis.com">
    <link rel="dns-prefetch" href="//staging.gomyhire.com.my">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="app.js" as="script">
    
    <!-- 关键CSS内联 -->
    <style>
        /* 关键路径CSS */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <!-- 非关键CSS延迟加载 -->
    <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="styles.css"></noscript>
</head>
<body>
    <!-- 内容 -->
    
    <!-- 脚本延迟加载 -->
    <script src="app.js" defer></script>
</body>
</html>
```

### 2. 图片优化

```javascript
class ImageOptimizer {
    static async compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // 计算新尺寸
                let { width, height } = this.calculateNewDimensions(
                    img.width, img.height, maxWidth, maxHeight
                );
                
                canvas.width = width;
                canvas.height = height;
                
                // 绘制压缩后的图片
                ctx.drawImage(img, 0, 0, width, height);
                
                // 转换为Blob
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
    
    static calculateNewDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        let width = originalWidth;
        let height = originalHeight;
        
        // 按比例缩放
        if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
        }
        
        if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
        }
        
        return { width: Math.round(width), height: Math.round(height) };
    }
    
    // 渐进式图片加载
    static setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // 图片预加载
    static preloadImages(urls) {
        const promises = urls.map(url => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = resolve;
                img.onerror = reject;
                img.src = url;
            });
        });
        
        return Promise.allSettled(promises);
    }
}
```

### 3. DOM操作优化

```javascript
class DOMOptimizer {
    // 批量DOM更新
    static batchDOMUpdates(updates) {
        const fragment = document.createDocumentFragment();
        
        updates.forEach(update => {
            if (update.type === 'create') {
                const element = document.createElement(update.tag);
                Object.assign(element, update.properties);
                fragment.appendChild(element);
            }
        });
        
        return fragment;
    }
    
    // 虚拟滚动实现
    static createVirtualScroller(container, items, itemHeight, renderItem) {
        const totalHeight = items.length * itemHeight;
        const viewportHeight = container.clientHeight;
        const visibleCount = Math.ceil(viewportHeight / itemHeight) + 2;
        
        let scrollTop = 0;
        let startIndex = 0;
        
        const viewport = document.createElement('div');
        viewport.style.height = `${totalHeight}px`;
        viewport.style.position = 'relative';
        
        const visibleItems = document.createElement('div');
        visibleItems.style.position = 'absolute';
        visibleItems.style.top = '0';
        visibleItems.style.width = '100%';
        
        viewport.appendChild(visibleItems);
        container.appendChild(viewport);
        
        function updateVisibleItems() {
            startIndex = Math.floor(scrollTop / itemHeight);
            const endIndex = Math.min(startIndex + visibleCount, items.length);
            
            visibleItems.innerHTML = '';
            visibleItems.style.transform = `translateY(${startIndex * itemHeight}px)`;
            
            for (let i = startIndex; i < endIndex; i++) {
                const item = renderItem(items[i], i);
                item.style.height = `${itemHeight}px`;
                visibleItems.appendChild(item);
            }
        }
        
        container.addEventListener('scroll', () => {
            scrollTop = container.scrollTop;
            requestAnimationFrame(updateVisibleItems);
        });
        
        updateVisibleItems();
        
        return {
            updateItems: (newItems) => {
                items = newItems;
                viewport.style.height = `${items.length * itemHeight}px`;
                updateVisibleItems();
            }
        };
    }
    
    // 防抖和节流
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}
```

## 🔄 缓存策略

### 1. 多层缓存架构

```javascript
class CacheManager {
    constructor() {
        this.memoryCache = new Map();
        this.sessionCache = sessionStorage;
        this.persistentCache = localStorage;
        this.indexedDBCache = null;
        
        this.initIndexedDB();
    }
    
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('OTACache', 1);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.indexedDBCache = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 创建对象存储
                const apiStore = db.createObjectStore('apiResponses', { keyPath: 'key' });
                apiStore.createIndex('timestamp', 'timestamp', { unique: false });
                
                const imageStore = db.createObjectStore('images', { keyPath: 'key' });
                imageStore.createIndex('timestamp', 'timestamp', { unique: false });
            };
        });
    }
    
    // 智能缓存策略
    async set(key, value, options = {}) {
        const {
            ttl = 3600000, // 1小时默认TTL
            storage = 'auto', // auto, memory, session, persistent, indexeddb
            compress = false
        } = options;
        
        const cacheItem = {
            key,
            value: compress ? this.compress(value) : value,
            timestamp: Date.now(),
            ttl,
            compressed: compress
        };
        
        const storageType = storage === 'auto' ? this.selectOptimalStorage(value) : storage;
        
        switch (storageType) {
            case 'memory':
                this.memoryCache.set(key, cacheItem);
                break;
            case 'session':
                this.sessionCache.setItem(key, JSON.stringify(cacheItem));
                break;
            case 'persistent':
                this.persistentCache.setItem(key, JSON.stringify(cacheItem));
                break;
            case 'indexeddb':
                await this.setIndexedDB(key, cacheItem);
                break;
        }
    }
    
    async get(key) {
        // 按优先级检查各层缓存
        let cacheItem = this.memoryCache.get(key);
        
        if (!cacheItem) {
            const sessionItem = this.sessionCache.getItem(key);
            if (sessionItem) {
                cacheItem = JSON.parse(sessionItem);
                // 提升到内存缓存
                this.memoryCache.set(key, cacheItem);
            }
        }
        
        if (!cacheItem) {
            const persistentItem = this.persistentCache.getItem(key);
            if (persistentItem) {
                cacheItem = JSON.parse(persistentItem);
                // 提升到内存缓存
                this.memoryCache.set(key, cacheItem);
            }
        }
        
        if (!cacheItem && this.indexedDBCache) {
            cacheItem = await this.getIndexedDB(key);
            if (cacheItem) {
                // 提升到内存缓存
                this.memoryCache.set(key, cacheItem);
            }
        }
        
        if (cacheItem) {
            // 检查TTL
            if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
                await this.delete(key);
                return null;
            }
            
            return cacheItem.compressed ? this.decompress(cacheItem.value) : cacheItem.value;
        }
        
        return null;
    }
    
    selectOptimalStorage(value) {
        const size = JSON.stringify(value).length;
        
        if (size < 1024) { // < 1KB
            return 'memory';
        } else if (size < 1024 * 100) { // < 100KB
            return 'session';
        } else if (size < 1024 * 1024) { // < 1MB
            return 'persistent';
        } else {
            return 'indexeddb';
        }
    }
    
    compress(data) {
        // 简单的压缩实现（实际项目中可使用更好的压缩算法）
        return btoa(JSON.stringify(data));
    }
    
    decompress(compressedData) {
        return JSON.parse(atob(compressedData));
    }
    
    // 缓存预热
    async warmup(keys) {
        const promises = keys.map(async (key) => {
            const cached = await this.get(key);
            if (!cached) {
                // 触发数据加载
                return this.loadAndCache(key);
            }
            return cached;
        });
        
        return Promise.allSettled(promises);
    }
    
    // 缓存清理
    async cleanup() {
        const now = Date.now();
        
        // 清理内存缓存
        for (const [key, item] of this.memoryCache.entries()) {
            if (now - item.timestamp > item.ttl) {
                this.memoryCache.delete(key);
            }
        }
        
        // 清理其他存储层
        await this.cleanupStorage(this.sessionCache);
        await this.cleanupStorage(this.persistentCache);
        await this.cleanupIndexedDB();
    }
}
```

### 2. API响应缓存

```javascript
class APICache {
    constructor(cacheManager) {
        this.cache = cacheManager;
        this.pendingRequests = new Map();
    }
    
    async cachedFetch(url, options = {}, cacheOptions = {}) {
        const cacheKey = this.generateCacheKey(url, options);
        
        // 检查缓存
        const cached = await this.cache.get(cacheKey);
        if (cached && !cacheOptions.forceRefresh) {
            return cached;
        }
        
        // 防止重复请求
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }
        
        // 发起请求
        const requestPromise = this.fetchWithRetry(url, options)
            .then(async (response) => {
                const data = await response.json();
                
                // 缓存响应
                await this.cache.set(cacheKey, data, {
                    ttl: cacheOptions.ttl || 300000, // 5分钟默认
                    storage: cacheOptions.storage || 'auto'
                });
                
                this.pendingRequests.delete(cacheKey);
                return data;
            })
            .catch((error) => {
                this.pendingRequests.delete(cacheKey);
                throw error;
            });
        
        this.pendingRequests.set(cacheKey, requestPromise);
        return requestPromise;
    }
    
    generateCacheKey(url, options) {
        const keyData = {
            url,
            method: options.method || 'GET',
            body: options.body,
            headers: options.headers
        };
        
        return btoa(JSON.stringify(keyData));
    }
    
    async fetchWithRetry(url, options, maxRetries = 3) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                const response = await fetch(url, options);
                if (response.ok) {
                    return response;
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            } catch (error) {
                lastError = error;
                if (i < maxRetries) {
                    await this.delay(Math.pow(2, i) * 1000); // 指数退避
                }
            }
        }
        
        throw lastError;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

## 📊 性能监控

### 1. 性能指标收集

```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.initMonitoring();
    }
    
    initMonitoring() {
        // 页面加载性能
        this.monitorPageLoad();
        
        // 资源加载性能
        this.monitorResourceLoad();
        
        // 用户交互性能
        this.monitorUserInteractions();
        
        // API调用性能
        this.monitorAPIPerformance();
        
        // 内存使用情况
        this.monitorMemoryUsage();
    }
    
    monitorPageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            this.recordMetric('page_load', {
                dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                tcp_connect: navigation.connectEnd - navigation.connectStart,
                request_response: navigation.responseEnd - navigation.requestStart,
                dom_parse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
                total_load_time: navigation.loadEventEnd - navigation.navigationStart,
                first_paint: this.getFirstPaint(),
                first_contentful_paint: this.getFirstContentfulPaint(),
                largest_contentful_paint: this.getLargestContentfulPaint()
            });
        });
    }
    
    monitorResourceLoad() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                if (entry.entryType === 'resource') {
                    this.recordMetric('resource_load', {
                        name: entry.name,
                        type: entry.initiatorType,
                        size: entry.transferSize,
                        duration: entry.duration,
                        start_time: entry.startTime
                    });
                }
            });
        });
        
        observer.observe({ entryTypes: ['resource'] });
        this.observers.set('resource', observer);
    }
    
    monitorUserInteractions() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                this.recordMetric('user_interaction', {
                    type: entry.name,
                    duration: entry.duration,
                    start_time: entry.startTime,
                    processing_start: entry.processingStart,
                    processing_end: entry.processingEnd
                });
            });
        });
        
        observer.observe({ entryTypes: ['event'] });
        this.observers.set('interaction', observer);
    }
    
    monitorAPIPerformance() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                
                this.recordMetric('api_call', {
                    url,
                    method: args[1]?.method || 'GET',
                    status: response.status,
                    duration: endTime - startTime,
                    success: response.ok
                });
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                
                this.recordMetric('api_call', {
                    url,
                    method: args[1]?.method || 'GET',
                    duration: endTime - startTime,
                    success: false,
                    error: error.message
                });
                
                throw error;
            }
        };
    }
    
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                this.recordMetric('memory_usage', {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });
            }, 30000); // 每30秒记录一次
        }
    }
    
    recordMetric(type, data) {
        if (!this.metrics.has(type)) {
            this.metrics.set(type, []);
        }
        
        const metrics = this.metrics.get(type);
        metrics.push({
            ...data,
            timestamp: Date.now()
        });
        
        // 限制存储的指标数量
        if (metrics.length > 1000) {
            metrics.splice(0, 500); // 删除前500个
        }
        
        // 检查性能阈值
        this.checkPerformanceThresholds(type, data);
    }
    
    checkPerformanceThresholds(type, data) {
        const thresholds = {
            page_load: { total_load_time: 3000 },
            api_call: { duration: 5000 },
            user_interaction: { duration: 100 },
            memory_usage: { used: 50 * 1024 * 1024 } // 50MB
        };
        
        const threshold = thresholds[type];
        if (!threshold) return;
        
        Object.keys(threshold).forEach(key => {
            if (data[key] > threshold[key]) {
                this.reportPerformanceIssue(type, key, data[key], threshold[key]);
            }
        });
    }
    
    reportPerformanceIssue(type, metric, actual, threshold) {
        console.warn(`性能警告: ${type}.${metric} = ${actual} 超过阈值 ${threshold}`);
        
        // 发送性能警报
        this.sendPerformanceAlert({
            type,
            metric,
            actual,
            threshold,
            timestamp: Date.now()
        });
    }
    
    async sendPerformanceAlert(alert) {
        try {
            await fetch('/api/performance/alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(alert)
            });
        } catch (error) {
            console.error('发送性能警报失败:', error);
        }
    }
    
    getPerformanceReport() {
        const report = {};
        
        this.metrics.forEach((metrics, type) => {
            if (metrics.length === 0) return;
            
            const values = metrics.map(m => m.duration || m.total_load_time || m.used).filter(v => v !== undefined);
            
            report[type] = {
                count: metrics.length,
                average: values.reduce((a, b) => a + b, 0) / values.length,
                min: Math.min(...values),
                max: Math.max(...values),
                p95: this.percentile(values, 0.95),
                p99: this.percentile(values, 0.99)
            };
        });
        
        return report;
    }
    
    percentile(values, p) {
        const sorted = values.sort((a, b) => a - b);
        const index = Math.ceil(sorted.length * p) - 1;
        return sorted[index];
    }
}
```

### 2. 实时性能仪表板

```javascript
class PerformanceDashboard {
    constructor(monitor) {
        this.monitor = monitor;
        this.charts = new Map();
        this.updateInterval = 5000; // 5秒更新一次
        
        this.createDashboard();
        this.startRealTimeUpdates();
    }
    
    createDashboard() {
        const dashboard = document.createElement('div');
        dashboard.id = 'performance-dashboard';
        dashboard.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            display: none;
        `;
        
        dashboard.innerHTML = `
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <h3 style="margin: 0;">性能监控</h3>
                <button id="close-dashboard" style="background: none; border: none; color: white; cursor: pointer;">×</button>
            </div>
            <div id="performance-metrics"></div>
        `;
        
        document.body.appendChild(dashboard);
        
        // 添加切换按钮
        const toggleButton = document.createElement('button');
        toggleButton.textContent = '性能';
        toggleButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10001;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        `;
        
        toggleButton.addEventListener('click', () => {
            dashboard.style.display = dashboard.style.display === 'none' ? 'block' : 'none';
        });
        
        document.getElementById('close-dashboard').addEventListener('click', () => {
            dashboard.style.display = 'none';
        });
        
        document.body.appendChild(toggleButton);
    }
    
    startRealTimeUpdates() {
        setInterval(() => {
            this.updateDashboard();
        }, this.updateInterval);
    }
    
    updateDashboard() {
        const report = this.monitor.getPerformanceReport();
        const metricsContainer = document.getElementById('performance-metrics');
        
        if (!metricsContainer) return;
        
        let html = '';
        
        // 页面加载性能
        if (report.page_load) {
            html += `
                <div style="margin-bottom: 10px;">
                    <strong>页面加载</strong><br>
                    平均: ${Math.round(report.page_load.average)}ms<br>
                    P95: ${Math.round(report.page_load.p95)}ms
                </div>
            `;
        }
        
        // API调用性能
        if (report.api_call) {
            html += `
                <div style="margin-bottom: 10px;">
                    <strong>API调用</strong><br>
                    平均: ${Math.round(report.api_call.average)}ms<br>
                    P95: ${Math.round(report.api_call.p95)}ms<br>
                    总数: ${report.api_call.count}
                </div>
            `;
        }
        
        // 内存使用
        if (report.memory_usage) {
            const avgMemory = report.memory_usage.average / (1024 * 1024);
            html += `
                <div style="margin-bottom: 10px;">
                    <strong>内存使用</strong><br>
                    平均: ${avgMemory.toFixed(1)}MB<br>
                    峰值: ${(report.memory_usage.max / (1024 * 1024)).toFixed(1)}MB
                </div>
            `;
        }
        
        // 用户交互性能
        if (report.user_interaction) {
            html += `
                <div style="margin-bottom: 10px;">
                    <strong>用户交互</strong><br>
                    平均: ${Math.round(report.user_interaction.average)}ms<br>
                    P95: ${Math.round(report.user_interaction.p95)}ms
                </div>
            `;
        }
        
        metricsContainer.innerHTML = html;
    }
}
```

## 🧪 性能测试

### 1. 自动化性能测试

```javascript
class PerformanceTester {
    constructor() {
        this.testResults = [];
        this.testSuites = new Map();
    }
    
    // 注册测试套件
    registerTestSuite(name, tests) {
        this.testSuites.set(name, tests);
    }
    
    // 运行所有测试
    async runAllTests() {
        const results = new Map();
        
        for (const [suiteName, tests] of this.testSuites) {
            console.log(`运行测试套件: ${suiteName}`);
            const suiteResults = await this.runTestSuite(tests);
            results.set(suiteName, suiteResults);
        }
        
        return results;
    }
    
    // 运行测试套件
    async runTestSuite(tests) {
        const results = [];
        
        for (const test of tests) {
            try {
                const result = await this.runTest(test);
                results.push(result);
            } catch (error) {
                results.push({
                    name: test.name,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }
    
    // 运行单个测试
    async runTest(test) {
        const startTime = performance.now();
        
        try {
            await test.fn();
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            const success = !test.maxDuration || duration <= test.maxDuration;
            
            return {
                name: test.name,
                success,
                duration,
                maxDuration: test.maxDuration,
                message: success ? '通过' : `超时: ${duration}ms > ${test.maxDuration}ms`
            };
        } catch (error) {
            const endTime = performance.now();
            
            return {
                name: test.name,
                success: false,
                duration: endTime - startTime,
                error: error.message
            };
        }
    }
    
    // 生成测试报告
    generateReport(results) {
        let report = '# 性能测试报告\n\n';
        
        for (const [suiteName, suiteResults] of results) {
            report += `## ${suiteName}\n\n`;
            
            const passed = suiteResults.filter(r => r.success).length;
            const total = suiteResults.length;
            
            report += `通过率: ${passed}/${total} (${Math.round(passed/total*100)}%)\n\n`;
            
            report += '| 测试名称 | 状态 | 耗时 | 说明 |\n';
            report += '|---------|------|------|------|\n';
            
            suiteResults.forEach(result => {
                const status = result.success ? '✅' : '❌';
                const duration = `${Math.round(result.duration)}ms`;
                const message = result.message || result.error || '';
                
                report += `| ${result.name} | ${status} | ${duration} | ${message} |\n`;
            });
            
            report += '\n';
        }
        
        return report;
    }
}

// 性能测试用例
const performanceTester = new PerformanceTester();

// 页面加载测试
performanceTester.registerTestSuite('页面加载', [
    {
        name: '首屏加载时间',
        maxDuration: 2000,
        fn: async () => {
            // 模拟页面加载
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve);
                }
            });
        }
    },
    {
        name: 'DOM解析时间',
        maxDuration: 1000,
        fn: async () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            const domParseTime = navigation.domContentLoadedEventEnd - navigation.responseEnd;
            
            if (domParseTime > 1000) {
                throw new Error(`DOM解析时间过长: ${domParseTime}ms`);
            }
        }
    }
]);

// API性能测试
performanceTester.registerTestSuite('API性能', [
    {
        name: '登录API响应时间',
        maxDuration: 3000,
        fn: async () => {
            const startTime = performance.now();
            
            // 模拟登录请求
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username: 'test', password: 'test' })
            });
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (duration > 3000) {
                throw new Error(`API响应时间过长: ${duration}ms`);
            }
        }
    },
    {
        name: 'Gemini API响应时间',
        maxDuration: 10000,
        fn: async () => {
            const geminiService = new GeminiService();
            const startTime = performance.now();
            
            await geminiService.detectOTAType('测试订单内容');
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (duration > 10000) {
                throw new Error(`Gemini API响应时间过长: ${duration}ms`);
            }
        }
    }
]);

// 内存使用测试
performanceTester.registerTestSuite('内存使用', [
    {
        name: '内存泄漏检测',
        fn: async () => {
            if (!performance.memory) {
                throw new Error('浏览器不支持内存监控');
            }
            
            const initialMemory = performance.memory.usedJSHeapSize;
            
            // 执行一些操作
            for (let i = 0; i < 1000; i++) {
                const div = document.createElement('div');
                div.innerHTML = 'test';
                document.body.appendChild(div);
                document.body.removeChild(div);
            }
            
            // 强制垃圾回收（如果支持）
            if (window.gc) {
                window.gc();
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const finalMemory = performance.memory.usedJSHeapSize;
            const memoryIncrease = finalMemory - initialMemory;
            
            if (memoryIncrease > 1024 * 1024) { // 1MB
                throw new Error(`可能存在内存泄漏: 增加了${Math.round(memoryIncrease/1024)}KB`);
            }
        }
    }
]);
```

## 📈 性能优化建议

### 1. 关键性能指标(KPI)

- **首屏加载时间(FCP)**: < 1.5秒
- **最大内容绘制(LCP)**: < 2.5秒
- **首次输入延迟(FID)**: < 100毫秒
- **累积布局偏移(CLS)**: < 0.1
- **API响应时间**: < 3秒
- **内存使用**: < 100MB

### 2. 优化优先级

1. **高优先级**
   - 减少关键路径资源
   - 优化图片加载
   - 实施有效缓存策略
   - 减少API调用次数

2. **中优先级**
   - 代码分割和懒加载
   - 服务工作者缓存
   - 数据库查询优化
   - CDN配置

3. **低优先级**
   - 微优化和代码清理
   - 高级缓存策略
   - 性能监控增强

### 3. 持续优化流程

```javascript
class ContinuousOptimization {
    constructor() {
        this.optimizationTasks = [];
        this.performanceBaseline = null;
        
        this.establishBaseline();
        this.scheduleOptimizations();
    }
    
    async establishBaseline() {
        // 建立性能基线
        const baseline = await this.measurePerformance();
        this.performanceBaseline = baseline;
        
        console.log('性能基线已建立:', baseline);
    }
    
    scheduleOptimizations() {
        // 每周运行性能优化检查
        setInterval(() => {
            this.runOptimizationCycle();
        }, 7 * 24 * 60 * 60 * 1000);
    }
    
    async runOptimizationCycle() {
        console.log('开始性能优化周期');
        
        // 1. 测量当前性能
        const currentPerformance = await this.measurePerformance();
        
        // 2. 与基线比较
        const regression = this.detectRegression(currentPerformance);
        
        // 3. 识别优化机会
        const opportunities = this.identifyOptimizationOpportunities(currentPerformance);
        
        // 4. 执行优化
        await this.executeOptimizations(opportunities);
        
        // 5. 验证优化效果
        const optimizedPerformance = await this.measurePerformance();
        
        // 6. 更新基线
        if (this.isImprovement(optimizedPerformance, currentPerformance)) {
            this.performanceBaseline = optimizedPerformance;
        }
        
        console.log('性能优化周期完成');
    }
    
    async measurePerformance() {
        // 实施性能测试并返回结果
        const results = await performanceTester.runAllTests();
        return this.aggregateResults(results);
    }
    
    detectRegression(current) {
        if (!this.performanceBaseline) return [];
        
        const regressions = [];
        
        Object.keys(current).forEach(metric => {
            const baseline = this.performanceBaseline[metric];
            const currentValue = current[metric];
            
            if (currentValue > baseline * 1.1) { // 10%性能下降
                regressions.push({
                    metric,
                    baseline,
                    current: currentValue,
                    regression: ((currentValue - baseline) / baseline * 100).toFixed(1) + '%'
                });
            }
        });
        
        return regressions;
    }
}
```

## 总结

性能优化是一个持续的过程，需要：

1. **建立监控体系**: 实时监控关键性能指标
2. **实施缓存策略**: 多层缓存减少重复计算和网络请求
3. **优化资源加载**: 压缩、懒加载、预加载等技术
4. **定期性能测试**: 自动化测试确保性能不退化
5. **持续优化**: 基于数据驱动的优化决策

通过系统性的性能优化，可以显著提升用户体验和系统效率。